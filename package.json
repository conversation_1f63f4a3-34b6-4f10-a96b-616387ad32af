{"name": "webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hotjar/browser": "^1.0.9", "@mantine/carousel": "^7.14.1", "@mantine/core": "^7.12.1", "@mantine/form": "^7.17.2", "@mantine/hooks": "^7.12.1", "@next/third-parties": "^15.0.3", "@tabler/icons-react": "^3.1.0", "embla-carousel-autoplay": "^8.5.1", "framer-motion": "^12.5.0", "next": "^15.3.3", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18", "react-icons": "^5.5.0", "react-parallax-mouse": "^2.1.0", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.1", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.1.4", "npm": "^10.5.0", "postcss": "^8.4.38", "postcss-preset-mantine": "^1.13.0", "postcss-simple-vars": "^7.0.1", "typescript": "^5"}}