export type { ApiResponse, ColectaResponse, SignupParams };

interface ColectaResponse {
    message?: string;
    result?: any;
    error?: any;
}

interface SignupParams {
    firstName: string;
    lastName: string;
    email: string;
    password?: string;
    isProdUser?: boolean;
    isDemo?: boolean;
    brandName?: string;
    phone?: string;
    socialHandler?: string;
}

interface ApiResponse {
    message: string;
    result?: any;
    error?: string;
}