import { ApiResponse } from "@/lib/types";

const toCamel = (s: any) => {
    return s.replace(/([-_][a-z])/ig, ($1: any) => {
        return $1.toUpperCase()
            .replace('-', '')
            .replace('_', '');
    });
};

const tooSnake = (str: string) =>
    str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);

const isArray = function (a: any) {
    return Array.isArray(a);
};

const isObject = function (o: Object) {
    return o === Object(o) && !isArray(o) && typeof o !== 'function';
};

const keysToCamel = function (o: any): any {
    if (isObject(o)) {
        const n = {} as Record<string, any>;

        Object.keys(o)
            .forEach((k) => {
                n[toCamel(k)] = keysToCamel(o[k]);
            });

        return n;
    } else if (isArray(o)) {
        return o.map((i: any) => {
            return keysToCamel(i);
        });
    }

    return o;
};



const keysToSnake = function (o: any): any {
    if (isObject(o)) {
        const n = {} as Record<string, any>;

        Object.keys(o)
            .forEach((k) => {
                n[tooSnake(k)] = keysToSnake(o[k]);
            });

        return n;
    } else if (isArray(o)) {
        return o.map((i: any) => {
            return keysToSnake(i);
        });
    }

    return o;
};

export const transformResponse = (res: any, reverse?: boolean): ApiResponse => {
    return reverse ? keysToSnake(res) : keysToCamel(res);
}