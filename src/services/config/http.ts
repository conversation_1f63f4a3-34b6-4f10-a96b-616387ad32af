import { transformResponse } from "./transform";

const baseURL = process.env.PUBLIC_URL;
const getBaseUrl = (m?: boolean) => {
    if (m) { return process.env.API_ENDPOINT }
    return baseURL;
}

const GET = async <T>(url: string, m?: boolean) => fetch(url, {
    method: 'GET'
}).then(transformResponse);

const POST = async <T>(url: string, body: any, m?: boolean) =>
    fetch(url,
        {
            method: 'POST', body: JSON.stringify(transformResponse(body, true))
        }).then(transformResponse);


export { GET, POST };

