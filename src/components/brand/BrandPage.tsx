"use client";

import { Stack } from "@mantine/core";
import FaqSection from "../common/sections/faqs/FaqSection";
import FeaturesSection from "./sections/features/FeaturesSection";
import BrandFooter from "./sections/footer/BrandFooter";
import GridCarouselSection from "./sections/grid-carousel/GridCarouselSection";
import HeroSection from "./sections/hero/HeroSection";
import HeroCardSection from "./sections/herocard/HeroCardSection";
import OldVsNewSection from "./sections/oldvsnew/OldVsNewSection";
import UspSection from "./sections/usp/UspSection";

const BrandPage = () => {
  return (
    <Stack>
      <HeroSection />
      <HeroCardSection />
      <UspSection />
      <OldVsNewSection />
      <FeaturesSection />
      <FaqSection isBrand />
      <GridCarouselSection />
      <BrandFooter />
    </Stack>
  );
};
export default BrandPage;
