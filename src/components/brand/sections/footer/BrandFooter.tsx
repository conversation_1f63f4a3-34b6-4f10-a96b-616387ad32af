import Button from "@/components/common/button/Button";
import { COLECTA_APP_REQUEST_DEMO_URL } from "@/components/common/constants/urls";
import { Box, Flex, rem, Stack, Text, Title } from "@mantine/core";
import Link from "next/link";
import classes from "./BrandFooter.module.css";

const BrandFooterMobile = () => {
  return (
    <Box className={classes.brandFooterContainerMobile} hiddenFrom="lg">
      <Stack gap={rem(20)} justify="space-between" h="95%">
        <Stack align="flex-end">
          <Title
            order={1}
            size={rem(28)}
            fw={400}
            fs="italic"
            className={classes.creatorFooterTitle}
          >
            Ready to Collaborate?
          </Title>
          <Text size={rem(16)} fw={300} w={rem("75%")} lh={rem(27)} ta="right">
            Explore the world of colecta.ai, and let all your needs be figured
            out with an intelligent approach.
          </Text>
        </Stack>
        <Link
          style={{ width: "100%" }}
          href={COLECTA_APP_REQUEST_DEMO_URL}
          target="_blank"
        >
          <Button mt="xl" fs="italic" size="xl" w="90%">
            Book A Demo
          </Button>
        </Link>
      </Stack>
    </Box>
  );
};

const BrandFooterDesktop = () => {
  return (
    <Box className={classes.brandFooterContainer} visibleFrom="lg">
      <Stack gap={rem(20)}>
        <Title
          order={1}
          size={rem(56)}
          fw={400}
          fs="italic"
          className={classes.brandFooterTitle}
          w={rem("50%")}
        >
          Ready to Build Lasting Brand Relationships?
        </Title>
        <Text size={rem(18)} fw={300} w={rem("45%")} lh={rem(32)}>
          Explore the world of colecta.ai, and let all your needs be figured out
          with an intelligent approach. Collaborate with creators who add value
          to your vision and contribute to your growth
        </Text>
        <Flex w="100%" mt={rem(60)} pl={rem(300)}>
          <Link
            style={{ width: "100%" }}
            href={COLECTA_APP_REQUEST_DEMO_URL}
            target="_blank"
          >
            <Button mt="xl" fs="italic" size="xl" w="100%">
              Book A Call For Demo
            </Button>
          </Link>
        </Flex>
      </Stack>
    </Box>
  );
};

const BrandFooter = () => {
  return (
    <>
      <BrandFooterDesktop />
      <BrandFooterMobile />
    </>
  );
};

export default BrandFooter;
