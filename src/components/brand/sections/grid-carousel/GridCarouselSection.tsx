import Button from "@/components/common/button/Button";
import { COLECTA_APP_REQUEST_DEMO_URL } from "@/components/common/constants/urls";
import { Carousel } from "@mantine/carousel";
import { Flex, Group, Image, Paper, rem, Stack, Text } from "@mantine/core";
import { IconArrowRight } from "@tabler/icons-react";
import Autoplay from "embla-carousel-autoplay";
import Link from "next/link";
import { useRef } from "react";
import classes from "./GridCarouselSection.module.css";

const GridCarouselSectionMobile = () => {
  const autoplay = useRef(Autoplay({ delay: 1000 }));
  return (
    <Paper mb="8%" py={rem(36)} bg="#F3EDF8" hiddenFrom="lg">
      <Stack justify="space-between" align="flex-start" p={rem(24)}>
        <Text
          size={rem(24)}
          fw={400}
          lh={rem(28)}
          fs="italic"
          className={classes.titleText}
        >
          Creators Curated to Elevate Your Brand
        </Text>
        <Text fw={400} size={rem(14)} w="90%" lh={rem(22)}>
          Discover a curated selection of top creators perfectly matched to
          bring your brand’s vision to life
        </Text>
        <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
          <Button
            mt="md"
            fs="italic"
            size="lg"
            color="#373737"
            fw={500}
            rightSection={
              <IconArrowRight
                style={{
                  width: rem(16),
                  height: rem(16),
                }}
              />
            }
          >
            Explore Creators
          </Button>
        </Link>
      </Stack>
      <Carousel
        mt="lg"
        slideGap="xs"
        slideSize="30%"
        loop
        withControls={false}
        plugins={[autoplay.current]}
      >
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-1.png"
            isMobile
            tags={["Cooking guide", "Stroy-teller"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-2.png"
            isMobile
            tags={["Savage comebacks", "Roasting Humour"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-3.png"
            isMobile
            tags={["Daily vlog", "Guide Video"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-4.png"
            isMobile
            tags={["Comedy skits", "Comedy reels"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-5.png"
            isMobile
            tags={["Latest fashion trends", "Styling hacks"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-1.png"
            isMobile
            tags={["Cooking guide", "Stroy-teller"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-2.png"
            isMobile
            tags={["Savage comebacks", "Roasting Humour"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-3.png"
            isMobile
            tags={["Daily vlog", "Guide Video"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-4.png"
            isMobile
            tags={["Comedy skits", "Comedy reels"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-5.png"
            isMobile
            tags={["Latest fashion trends", "Styling hacks"]}
          />
        </Carousel.Slide>
      </Carousel>
    </Paper>
  );
};

const GridCarouselSectionDesktop = () => {
  const autoplay = useRef(Autoplay({ delay: 1500 }));

  return (
    <Paper mb="8%" py={rem(76)} bg="#F3EDF8" visibleFrom="lg">
      <Flex
        justify="space-between"
        align="flex-start"
        px={rem(112)}
        pb={rem(40)}
      >
        <Stack>
          <Text
            size={rem(40)}
            fw={400}
            lh={rem(48)}
            fs="italic"
            className={classes.titleText}
          >
            Creators Curated to Elevate Your Brand
          </Text>
          <Text fw={400} size={rem(20)} w="70%" lh={rem(32)}>
            Discover a curated selection of top creators perfectly matched to
            bring your brand’s vision to life
          </Text>
        </Stack>
        <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
          <Button
            fs="italic"
            size="md"
            color="#373737"
            fw={500}
            rightSection={
              <IconArrowRight
                style={{
                  width: rem(16),
                  height: rem(16),
                }}
              />
            }
          >
            Explore Creators
          </Button>
        </Link>
      </Flex>
      <Carousel
        slideGap="md"
        slideSize="18%"
        loop
        withControls={false}
        plugins={[autoplay.current]}
        onMouseEnter={autoplay.current.stop}
        onMouseLeave={autoplay.current.reset}
      >
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-1.png"
            tags={["Cooking guide", "Stroy-teller"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-2.png"
            tags={["Savage comebacks", "Roasting Humour"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-3.png"
            tags={["Daily vlog", "Guide Video"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-4.png"
            tags={["Comedy skits", "Comedy reels"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-5.png"
            tags={["Latest fashion trends", "Styling hacks"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-1.png"
            tags={["Cooking guide", "Stroy-teller"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-2.png"
            tags={["Savage comebacks", "Roasting Humour"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-3.png"
            tags={["Daily vlog", "Guide Video"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-4.png"
            tags={["Comedy skits", "Comedy reels"]}
          />
        </Carousel.Slide>
        <Carousel.Slide>
          <ImageContainer
            url="/creators/creators-5.png"
            tags={["Latest fashion trends", "Styling hacks"]}
          />
        </Carousel.Slide>
      </Carousel>
    </Paper>
  );
};

const ImageContainer = ({
  url,
  isMobile,
  tags,
}: {
  url: string;
  tags: string[];
  isMobile?: boolean;
}) => {
  return (
    <Paper
      p={isMobile ? rem(12) : rem(24)}
      radius={rem(12)}
      w={isMobile ? rem(150) : rem(272)}
      withBorder
    >
      <Stack justify="center" gap={isMobile ? rem(10) : "md"}>
        <Image src={url} />
        <Group gap={rem(10)}>
          <Paper
            bg="#F4F3FF"
            c="#5925DC"
            px={rem(10)}
            py={rem(4)}
            radius={rem(16)}
          >
            <Text size={rem(10)}>{tags[0]}</Text>
          </Paper>
          <Paper
            bg="#E5F8EC"
            c="#027A48"
            px={rem(10)}
            py={rem(4)}
            radius={rem(16)}
          >
            <Text size={rem(10)}>{tags[1]}</Text>
          </Paper>
        </Group>
      </Stack>
    </Paper>
  );
};

const GridCarouselSection = () => {
  return (
    <>
      <GridCarouselSectionDesktop />
      <GridCarouselSectionMobile />
    </>
  );
};

export default GridCarouselSection;
