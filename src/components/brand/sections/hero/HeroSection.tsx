import Button from "@/components/common/button/Button";
import { COLECTA_APP_REQUEST_DEMO_URL } from "@/components/common/constants/urls";
import { Center, Container, rem, Stack, Text, Title } from "@mantine/core";
import { IconArrowRight } from "@tabler/icons-react";
import Link from "next/link";
import HeroImagesSection from "./HeroImagesSection";
import classes from "./HeroSection.module.css";

const HeroSectionMobile = () => {
  return (
    <Stack align="center" hiddenFrom="lg">
      <Container className={classes.heroSectionTitleContainer}>
        <Stack align="center" ta="center">
          <Title order={1} size={rem(26)} fw={400} w="90%">
            Discover Creators who deliver desired results
          </Title>
          <Center>
            <Text fw={300} size={rem(14)} lh={rem(20)} ta="center" maw="75%">
              Identify the creators who align with your interests to help your
              brand stand out. The right choice is what makes the real
              difference!
            </Text>
          </Center>
          <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
            <Button
              mt="xl"
              fs="italic"
              size="lg"
              color="#E7C1FE"
              fw={500}
              c="#000"
              rightSection={
                <IconArrowRight
                  style={{
                    width: rem(16),
                    height: rem(16),
                    marginLeft: rem(36),
                  }}
                />
              }
            >
              Explore Creators
            </Button>
          </Link>
        </Stack>
      </Container>
      <HeroImagesSection isMobile />
    </Stack>
  );
};

const HeroSectionDesktop = () => {
  return (
    <Stack align="center" visibleFrom="lg">
      <Container className={classes.heroSectionTitleContainer}>
        <Stack align="center">
          <Title order={1} size={rem(46)} fw={400}>
            Discover Creators Who Deliver Desired Results
          </Title>
          <Center>
            <Text fw={300} size={rem(20)} lh={rem(28)} ta="center" maw="75%">
              Identify the creators who align with your interests to help your
              brand stand out. The right choice is what makes the real
              difference!
            </Text>
          </Center>
          <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
            <Button
              mt="xl"
              fs="italic"
              size="md"
              color="#E7C1FE"
              c="#000"
              fw={500}
              rightSection={
                <IconArrowRight
                  style={{
                    width: rem(16),
                    height: rem(16),
                    marginLeft: rem(36),
                  }}
                />
              }
            >
              Explore Creators
            </Button>
          </Link>
        </Stack>
      </Container>
      <HeroImagesSection />
    </Stack>
  );
};

const HeroSection = () => {
  return (
    <>
      <HeroSectionDesktop />
      <HeroSectionMobile />
    </>
  );
};

export default HeroSection;
