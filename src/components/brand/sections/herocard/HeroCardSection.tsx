import {
  Badge,
  Box,
  Grid,
  Image,
  Paper,
  rem,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import classes from "./HeroCardSection.module.css";

const HeroCard = ({
  url,
  title,
  desc,
}: {
  url: string;
  title: string;
  desc: string;
}) => {
  return (
    <Paper className={classes.cardImageContainer} withBorder>
      <Stack h="100%" gap="0">
        <Box className={classes.cardImage}>
          <Image src={url} h="100%" radius={rem(16)} />
        </Box>
        <Stack gap={rem(8)} px={rem(16)} pb={rem(24)} pt={rem(16)}>
          <Text size={rem(20)} fw={400}>
            {title}
          </Text>
          <Text
            size={rem(16)}
            fw={400}
            c="#00000080"
            lh={rem(24)}
            w="90%"
            className={classes.textDesc}
          >
            {desc}
          </Text>
        </Stack>
      </Stack>
    </Paper>
  );
};

const HeroCardMobile = ({
  url,
  title,
  desc,
}: {
  url: string;
  title: string;
  desc: string;
}) => {
  return (
    <Paper className={classes.cardImageContainerMobile} withBorder>
      <Stack h="100%" gap="0">
        <Box className={classes.cardImage}>
          <Image src={url} h="100%" radius={rem(8)} />
        </Box>
        <Stack gap={rem(8)} p={rem(8)}>
          <Text size={rem(14)} fw={400}>
            {title}
          </Text>
          <Text size={rem(12)} fw={400} c="#00000080" lh={rem(16)}>
            {desc}
          </Text>
        </Stack>
      </Stack>
    </Paper>
  );
};

const HeroCardSection = () => {
  return (
    <>
      <HeroCardSectionDesktop />
      <HeroCardSectionMobile />
    </>
  );
};

const HeroCardSectionDesktop = () => {
  return (
    <Paper
      mt="5%"
      mx="5%"
      withBorder
      radius={rem(72)}
      p={rem(96)}
      visibleFrom="lg"
    >
      <Stack align="center">
        <Badge color="#D795FF" variant="outline" size="lg">
          <Text c="#B34EF1" fw={600}>
            AI Powered Collaboration
          </Text>
        </Badge>
        <Title order={2} size={rem(46)} fw={400} w="50%" ta="center">
          Discover a Smarter Way to Connect with Creators
        </Title>
        <Text fw={400} size={rem(22)} w="55%" lh={rem(36)} ta="center">
          See how we simplify the process, allowing you to focus on building
          relationships that drive results.
        </Text>
      </Stack>
      <Grid mt={rem(64)} gutter="xl" grow>
        <Grid.Col span={5}>
          <HeroCard
            url="/campaign-dashboard.png"
            title="Campaign Management"
            desc="Simplify workflow with campaign management. From selection to tracking, streamline every step for impact."
          />
        </Grid.Col>
        <Grid.Col span={6}>
          <HeroCard
            url="/find-creators.png"
            title="AI Creator Discovery"
            desc="Find perfect creators effortlessly with AI-powered discovery. Match with talent that aligns with your goals and audience."
          />
        </Grid.Col>
        <Grid.Col span={6}>
          <HeroCard
            url="/creator-profile.png"
            title="Analyzed Data"
            desc="Analyzed Data delivers actionable insights, helping you measure performance and refine your campaigns effectively."
          />
        </Grid.Col>
        <Grid.Col span={5}>
          <HeroCard
            url="/setup-campaign.png"
            title="AI Budget Allocation"
            desc="AI Budget Allocation optimizes spending, ensuring maximum impact and measurable results."
          />
        </Grid.Col>
      </Grid>
    </Paper>
  );
};
const HeroCardSectionMobile = () => {
  return (
    <Paper mt="5%" mx="5%" radius={rem(24)} p={rem(24)} hiddenFrom="lg">
      <Stack align="flex-start">
        <Badge color="#D795FF" variant="outline" size="md">
          <Text c="#B34EF1" fw={600} size="sm">
            AI Powered Collaboration
          </Text>
        </Badge>
        <Title order={2} size={rem(26)} fw={400} w="90%">
          Discover a Smarter Way to Connect with Creators
        </Title>
        <Text fw={400} size={rem(14)} w="85%" lh={rem(20)}>
          See how we simplify the process, allowing you to focus on building
          relationships that drive results.
        </Text>
      </Stack>
      <Grid mt={rem(20)} gutter="xl" grow>
        <Grid.Col span={12}>
          <HeroCardMobile
            url="/setup-campaign.png"
            title="AI Budget Allocation"
            desc="AI Budget Allocation optimizes spending, ensuring maximum impact and measurable results."
          />
        </Grid.Col>
        <Grid.Col span={12}>
          <HeroCardMobile
            url="/campaign-dashboard.png"
            title="Campaign Management"
            desc="Simplify workflow with campaign management. From selection to tracking, streamline every step for impact."
          />
        </Grid.Col>
      </Grid>
    </Paper>
  );
};

export default HeroCardSection;
