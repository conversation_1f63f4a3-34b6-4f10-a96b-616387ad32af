import Button from "@/components/common/button/Button";
import { COLECTA_APP_REQUEST_DEMO_URL } from "@/components/common/constants/urls";
import { Box, Flex, Group, List, rem, Stack, Text, Title } from "@mantine/core";
import { IconArrowRight } from "@tabler/icons-react";
import Link from "next/link";
import classes from "./OldVsNewSection.module.css";

const OldVsNewTitleMobile = () => {
  return (
    <Flex justify="center" hiddenFrom="lg">
      <Stack align="center" gap={rem(8)} ta="center">
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          The New Way vs. The Old Way
        </Text>
        <Title order={1} size={rem(34)} fw={400} w="80%" tt="capitalize">
          Connect with creators in an intelligent way- The AI way
        </Title>
        <Text
          fw={400}
          lh={rem(22)}
          size={rem(14)}
          c="#000000CC"
          ta="center"
          w="90%"
        >
          Our AI-enhanced creator discovery streamlines the process of
          understanding your brand’s needs and helps you to choose the creators
          who perfectly suit you in no time, so you can focus on establishing
          strong relationships that yield effective results.
        </Text>
        <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
          <Button
            mt="xl"
            fs="italic"
            size="lg"
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
                stroke={1.5}
              />
            }
          >
            Explore "The AI Way"
          </Button>
        </Link>
      </Stack>
    </Flex>
  );
};
const OldVsNewTitleDesktop = () => {
  return (
    <Flex justify="center" visibleFrom="lg">
      <Stack align="center" gap={rem(8)}>
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          INNOVATIVE SOLUTIONS V/S CONVENTIONAL METHODS
        </Text>
        <Title order={1} size={rem(36)} fw={400} tt="capitalize">
          Connect with creators in an intelligent way- The AI way
        </Title>
        <Text
          fw={400}
          lh={rem(25)}
          size={rem(16)}
          c="#000000CC"
          ta="center"
          w="60%"
        >
          Here we are! Our AI-enhanced creator discovery streamlines the process
          of understanding your brand’s needs and helps you to choose the
          creators who perfectly suit you in no time, so you can focus on
          establishing strong relationships that yield effective results. Easy
          as a cakewalk!
        </Text>
        <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
          <Button
            mt="xl"
            fs="italic"
            size="md"
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
                stroke={1.5}
              />
            }
          >
            Explore "The AI Way"
          </Button>
        </Link>
      </Stack>
    </Flex>
  );
};

const OldVsNewContentSectionMobile = () => {
  return (
    <Stack
      mx={rem(24)}
      mb={rem(24)}
      gap="0"
      hiddenFrom="lg"
      style={{ position: "relative", top: "-60px" }}
    >
      <Box p={rem(36)} className={classes.topContainer} h={rem(380)}>
        <Stack gap={rem(16)}>
          <Text
            size={rem(24)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Conventional Methods
          </Text>
          <List size="md">
            <List.Item c="#00000099" my="md">
              Time-consuming searches
            </List.Item>
            <List.Item c="#00000099" my="md">
              Lack of certainty- unclear impact
            </List.Item>
            <List.Item c="#00000099" my="md">
              Standardized model of collaborations
            </List.Item>
            <List.Item c="#00000099" my="md">
              Brief Communication and minimal connection
            </List.Item>
          </List>
        </Stack>
      </Box>
      <Box
        py={rem(24)}
        px={rem(36)}
        className={classes.bottomContainer}
        h={rem("100%")}
      >
        <Stack gap={rem(16)}>
          <Text
            size={rem(24)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Innovative Methods
          </Text>
          <List size="md">
            <List.Item c="#00000099" my="md">
              Spontaneous insights and easy discoveries
            </List.Item>
            <List.Item c="#00000099" my="md">
              Outcome-oriented partnerships
            </List.Item>
            <List.Item c="#00000099" my="md">
              Adapted and personalized partnerships
            </List.Item>
            <List.Item c="#00000099" my="md">
              Foundation relying on trust to go a long way
            </List.Item>
          </List>
        </Stack>
      </Box>
    </Stack>
  );
};

const OldVsNewContentSectionDesktop = () => {
  return (
    <Group ml="15%" mr="3%" mt="5%" grow gap="0" visibleFrom="lg">
      <Box
        py={rem(52)}
        px={rem(60)}
        className={classes.leftContainer}
        maw="45%"
        h={rem(420)}
      >
        <Stack gap={rem(40)}>
          <Text
            size={rem(38)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Conventional Methods
          </Text>
          <List size="xl" w="90%">
            <List.Item c="#00000099" my="md">
              Time-consuming searches
            </List.Item>
            <List.Item c="#00000099" my="md">
              Lack of certainty- unclear impact
            </List.Item>
            <List.Item c="#00000099" my="md">
              Standardized model of collaborations
            </List.Item>
            <List.Item c="#00000099" my="md">
              Brief Communication and minimal connection
            </List.Item>
          </List>
        </Stack>
      </Box>
      <Box
        className={classes.rightContainer}
        px={rem(72)}
        py={rem(52)}
        h={rem(420)}
      >
        <Stack gap={rem(40)}>
          <Text
            size={rem(38)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Innovative Methods
          </Text>
          <List size="xl" pl="xl">
            <List.Item c="#00000099" my="md">
              Spontaneous insights and easy discoveries
            </List.Item>
            <List.Item c="#00000099" my="md">
              Outcome-oriented partnerships
            </List.Item>
            <List.Item c="#00000099" my="md">
              Adapted and personalized partnerships
            </List.Item>
            <List.Item c="#00000099" my="md">
              Foundation relying on trust to go a long way
            </List.Item>
          </List>
        </Stack>
      </Box>
    </Group>
  );
};

const OldVsNewSection = () => {
  return (
    <Stack mt="10%">
      <>
        <OldVsNewTitleDesktop />
        <OldVsNewTitleMobile />
        <OldVsNewContentSectionMobile />
        <OldVsNewContentSectionDesktop />
      </>
    </Stack>
  );
};

export default OldVsNewSection;
