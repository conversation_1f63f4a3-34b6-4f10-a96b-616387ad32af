import Button from "@/components/common/button/Button";
import { COLECTA_APP_REQUEST_DEMO_URL } from "@/components/common/constants/urls";
import { Flex, Grid, rem, Stack, Text, Title } from "@mantine/core";
import { IconArrowRight } from "@tabler/icons-react";
import Link from "next/link";
import AnalysisUsp from "./AnalysisUsp";
import DiscoveryUsp from "./DiscoveryUsp";

const UspSectionTitleMobile = () => {
  return (
    <Flex justify="center" hiddenFrom="lg">
      <Stack align="center" gap={rem(8)} ta="center">
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          Looking for your perfect match
        </Text>
        <Title order={1} size={rem(34)} fw={400} w="70%">
          Real Influence Isn’t Just A Number
        </Title>
        <Text
          fw={400}
          lh={rem(22)}
          size={rem(14)}
          c="#000000CC"
          ta="center"
          w="90%"
        >
          Amidst countless creators online, connecting with the one who provides
          real engagement is important, because it’s all about connection and
          not just numbers. Authentic connections always deliver better results.
        </Text>
        <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
          <Button
            mt="lg"
            fs="italic"
            size="lg"
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
                stroke={1.5}
              />
            }
          >
            Explore Creators
          </Button>
        </Link>
      </Stack>
    </Flex>
  );
};
const UspSectionTitleDesktop = () => {
  return (
    <Flex justify="center" visibleFrom="lg">
      <Stack align="center" gap={rem(8)}>
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          Looking for your perfect match
        </Text>
        <Title order={1} size={rem(36)} fw={400}>
          Real Influence Isn’t Just A Number
        </Title>
        <Text
          fw={400}
          lh={rem(25)}
          size={rem(16)}
          c="#000000CC"
          ta="center"
          w="60%"
        >
          Amidst countless creators online, connecting with the one who provides
          real engagement is important, because it’s all about connection and
          not just numbers. Authentic connections always deliver better results.
        </Text>
        <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
          <Button
            mt="xl"
            fs="italic"
            size="md"
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
                stroke={1.5}
              />
            }
          >
            Explore Creators
          </Button>
        </Link>
      </Stack>
    </Flex>
  );
};

const UspSection = () => {
  return (
    <Stack mt="10%">
      <UspSectionTitleDesktop />
      <UspSectionTitleMobile />
      <Grid
        gutter={{ base: rem(36), md: rem(120) }}
        overflow="hidden"
        mt={{ base: rem(90), md: rem(120) }}
      >
        <Grid.Col span={{ base: 12, md: 5 }}>
          {/* Desktop view */}
          <Stack ml={rem("20%")} mt={rem("12%")} visibleFrom="lg">
            <Title order={2} size={rem(28)} fw={400}>
              Discover Creators With AI Precision
            </Title>
            <Text size={rem(18)} fw={400} lh={rem(32)} c="#00000080">
              Colecta’s AI driven discovery enables to find you the creators who
              suit your goal in the best manner possible- from targeting the
              right audience to delivering impactful content. Here’s to enabling
              you to build customized, results-oriented collaborations without
              wasting resources, ensuring that every creator partnership is
              optimized for your success.
            </Text>
          </Stack>
          {/* Mobile view */}
          <Stack mr={rem(24)} hiddenFrom="lg" align="flex-end" ta="right">
            <Title order={2} size={rem(22)} fw={400}>
              Discover Creators With AI Precision
            </Title>
            <Text size={rem(14)} fw={400} lh={rem(22)} c="#00000080" w="90%">
              Colecta’s AI driven discovery enables to find you the creators who
              suit your goal in the best manner possible- from targeting the
              right audience to delivering impactful content.
            </Text>
          </Stack>
        </Grid.Col>
        <Grid.Col span={{ base: 11, md: 7 }} offset={{ base: 1, md: 0 }}>
          <DiscoveryUsp />
        </Grid.Col>
      </Grid>
      <Grid
        gutter={{ base: rem(36), md: rem(120) }}
        overflow="hidden"
        mt={{ base: rem(90), md: rem(120) }}
      >
        <Grid.Col span={{ base: 11, md: 7 }} visibleFrom="lg">
          <AnalysisUsp />
        </Grid.Col>
        <Grid.Col span={{ base: 12, md: 5 }}>
          {/* Desktop view */}
          <Stack mr={rem("20%")} mt={rem("12%")} visibleFrom="lg">
            <Title order={2} size={rem(28)} fw={400}>
              In-Depth Review of Creator Performance
            </Title>
            <Text size={rem(18)} fw={400} lh={rem(32)} c="#00000080">
              Colecta. ai gives a comprehensive analysis on the valuable data of
              creator’s performance and highlights metrics like the audience one
              appeals to and engagement rate one generates. With these useful
              insights, it becomes an instantly convenient choice for you to opt
              for the best fit for your brand’s unique needs in order to drive
              meaningful business growth.
            </Text>
          </Stack>
          {/* Mobile view */}
          <Stack ml={rem(24)} hiddenFrom="lg">
            <Title order={2} size={rem(22)} fw={400}>
              In-Depth Review of Creator Performance
            </Title>
            <Text size={rem(14)} fw={400} lh={rem(22)} c="#00000080" w="90%">
              Colecta. ai gives a comprehensive analysis on the valuable data of
              creator’s performance and highlights metrics like the audience one
              appeals to and engagement rate one generates.
            </Text>
          </Stack>
        </Grid.Col>
        <Grid.Col span={{ base: 11, md: 7 }} hiddenFrom="lg">
          <AnalysisUsp />
        </Grid.Col>
      </Grid>
    </Stack>
  );
};

export default UspSection;
