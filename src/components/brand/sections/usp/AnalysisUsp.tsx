import {
  Avatar,
  ColorSwatch,
  Divider,
  Flex,
  Grid,
  Group,
  Paper,
  rem,
  RingProgress,
  Stack,
  Text,
} from "@mantine/core";
import classes from "./UspSection.module.css";

const CreatorCard = ({ isMobile }: { isMobile?: boolean }) => {
  return (
    <Paper
      withBorder
      p={isMobile ? "sm" : "md"}
      radius={rem(12)}
      style={{ borderColor: "#0000000F" }}
      w={isMobile ? rem(380) : "100%"}
    >
      <Stack gap={isMobile ? rem(8) : "md"}>
        <Flex gap="md">
          <Avatar
            src="/skincare-creator1.png"
            radius={isMobile ? rem(6) : rem(12)}
            size={isMobile ? rem(28) : rem(52)}
          />
          <Stack gap={rem(2)}>
            <Text size={isMobile ? rem(9) : "lg"} fw={500}>
              <PERSON><PERSON> <PERSON>
            </Text>
            <Flex align="center" gap={rem(10)}>
              <Text c="#32323280" size={isMobile ? rem(8) : "sm"} mt={rem(2)}>
                Downtown London
              </Text>
              <Text c="#32323280" size={isMobile ? rem(8) : "sm"} mt={rem(2)}>
                |
              </Text>
              <Text c="#32323280" size={isMobile ? rem(8) : "sm"} mt={rem(2)}>
                Dermatology
              </Text>
              <Text c="#32323280" size={isMobile ? rem(8) : "sm"} mt={rem(2)}>
                |
              </Text>
              <Text c="#32323280" size={isMobile ? rem(8) : "sm"} mt={rem(2)}>
                Volggger
              </Text>
            </Flex>
          </Stack>
        </Flex>
        <Text size={isMobile ? rem(10) : rem(12)}>
          Hey there! 👋 Welcome to my channel! I'm Dr. Hannah Kopelman. I'm so
          excited to take you on this jo...
        </Text>
        <Grid mt={isMobile ? rem(2) : "sm"}>
          <Grid.Col span={2}>
            <Text c="#5E5F5E" size={isMobile ? rem(7) : "md"} mt={rem(2)}>
              Skills
            </Text>
          </Grid.Col>
          <Grid.Col span={10}>
            <Group gap={isMobile ? rem(2) : "xs"}>
              <Paper
                bg="#ECFDF3"
                c="#027A48"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>76% match</Text>
              </Paper>
              <Paper
                bg="#F4F3FF"
                c="#5925DC"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>Top Collaborator</Text>
              </Paper>
              <Paper
                bg="#F2F4F7"
                c="#344054"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>Skincare</Text>
              </Paper>
              <Paper
                bg="#F2F4F7"
                c="#344054"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>Dermatology</Text>
              </Paper>
              <Paper
                bg="#F2F4F7"
                c="#344054"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>Advice</Text>
              </Paper>
            </Group>
          </Grid.Col>
        </Grid>
        <Grid mt={isMobile ? rem(2) : "sm"}>
          <Grid.Col span={2}>
            <Text c="#5E5F5E" size={isMobile ? rem(7) : "md"} mt={rem(2)}>
              Creator Niche
            </Text>
          </Grid.Col>
          <Grid.Col span={10}>
            <Group gap={isMobile ? rem(2) : "xs"}>
              <Paper
                bg="#ECFDF3"
                c="#027A48"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>Healthy Recipes</Text>
              </Paper>
              <Paper
                bg="#FEF3F2"
                c="#B42318"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>Dermatology</Text>
              </Paper>
              <Paper
                bg="#F0F9FF"
                c="#026AA2"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>Advice</Text>
              </Paper>
              <Paper
                bg="#F4F3FF"
                c="#5925DC"
                px={isMobile ? rem(6) : rem(10)}
                py={isMobile ? rem(2) : rem(4)}
                radius={isMobile ? rem(8) : rem(16)}
              >
                <Text size={isMobile ? rem(8) : "sm"}>Skincare</Text>
              </Paper>
            </Group>
          </Grid.Col>
        </Grid>
      </Stack>
    </Paper>
  );
};

const CreatorDataCard1 = ({ isMobile }: { isMobile?: boolean }) => {
  return (
    <Paper
      withBorder
      p={isMobile ? rem(10) : rem(20)}
      radius={isMobile ? rem(6) : rem(10)}
      style={{ borderColor: "#0000000F" }}
      w={isMobile ? rem("30%") : rem("49%")}
    >
      <Stack
        gap={isMobile ? "xs" : rem(18)}
        align={isMobile ? "flex-end" : "flex-start"}
        pr={isMobile ? "xl" : "0"}
      >
        <Divider label="Youtube" labelPosition="left" />
        <Flex gap={rem(50)}>
          <Stack gap={rem(6)}>
            <Text size={isMobile ? rem(14) : rem(20)} fw={400} c="#454443">
              10.2%
            </Text>
            <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#667085">
              Engagement Rate
            </Text>
          </Stack>
          <Stack gap={rem(6)}>
            <Text size={isMobile ? rem(12) : rem(20)} fw={400} c="#454443">
              45k
            </Text>
            <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#667085">
              Average Reach
            </Text>
          </Stack>
        </Flex>
        <Divider label="Instagram" labelPosition="left" />
        <Flex gap={rem(50)}>
          <Stack gap={rem(6)}>
            <Text size={isMobile ? rem(14) : rem(20)} fw={400} c="#454443">
              14.8%
            </Text>
            <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#667085">
              Engagement Rate
            </Text>
          </Stack>
          <Stack gap={rem(6)}>
            <Text size={isMobile ? rem(12) : rem(20)} fw={400} c="#454443">
              178k
            </Text>
            <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#667085">
              Average View
            </Text>
          </Stack>
        </Flex>
      </Stack>
    </Paper>
  );
};

const CreatorDataCard2 = ({ isMobile }: { isMobile?: boolean }) => {
  return (
    <Paper
      withBorder
      p={isMobile ? rem(10) : rem(20)}
      radius={isMobile ? rem(6) : rem(10)}
      style={{ borderColor: "#0000000F" }}
      w={isMobile ? rem("70%") : rem("49%")}
    >
      <Stack>
        <Flex align="center" gap="xs">
          <Text size={isMobile ? rem(10) : "lg"} fw={500} c="#454443">
            Audience Interests
          </Text>
          <Text
            size={isMobile ? rem(8) : "sm"}
            fw={400}
            c="#5E5E5E"
            mt={rem(2)}
          >
            Last 30 days
          </Text>
        </Flex>
        <Flex>
          <Stack w="50%" gap="xs">
            <Flex align="center" justify="space-between">
              <Group gap="xs">
                <ColorSwatch color="#FFA600" size={isMobile ? 6 : 10} />
                <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                  Skincare
                </Text>
              </Group>
              <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                - 37%
              </Text>
            </Flex>
            <Flex align="center" justify="space-between">
              <Group gap="xs">
                <ColorSwatch color="#58508D" size={isMobile ? 6 : 10} />
                <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                  Fitness & Health
                </Text>
              </Group>
              <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                - 14%
              </Text>
            </Flex>
            <Flex align="center" justify="space-between">
              <Group gap="xs">
                <ColorSwatch color="#9E67E3" size={isMobile ? 6 : 10} />
                <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                  Food & Cooking
                </Text>
              </Group>
              <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                - 19%
              </Text>
            </Flex>
            <Flex align="center" justify="space-between">
              <Group gap="xs">
                <ColorSwatch color="#C0CA33" size={isMobile ? 6 : 10} />
                <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                  Beauty
                </Text>
              </Group>
              <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                - 18%
              </Text>
            </Flex>
            <Flex align="center" justify="space-between">
              <Group gap="xs">
                <ColorSwatch color="#B42318" size={isMobile ? 6 : 10} />
                <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                  Travel Adventure
                </Text>
              </Group>
              <Text size={isMobile ? rem(8) : "sm"} fw={400} c="#222222">
                - 12%
              </Text>
            </Flex>
          </Stack>
          <RingProgress
            sections={[
              { value: 37, color: "#FFA600", tooltip: "Skincare" },
              { value: 14, color: "#58508D", tooltip: "Fitness & Health" },
              { value: 19, color: "#9E67E3", tooltip: "Food & Cooking" },
              { value: 18, color: "#C0CA33", tooltip: "Beauty" },
              { value: 12, color: "#B42318", tooltip: "Travel Adventure" },
            ]}
            size={isMobile ? 110 : 130}
            thickness={isMobile ? 8 : 10}
            label={
              <Stack gap="0">
                <Text
                  size={isMobile ? rem(12) : rem(17)}
                  fw={400}
                  ta="center"
                  px="xs"
                  style={{ pointerEvents: "none" }}
                >
                  425k
                </Text>
                <Text
                  size={isMobile ? rem(7) : "xs"}
                  fw={400}
                  ta="center"
                  px="xs"
                  c="#00000099"
                  style={{ pointerEvents: "none" }}
                >
                  Total audience
                </Text>
              </Stack>
            }
            ml="lg"
          />
        </Flex>
      </Stack>
    </Paper>
  );
};

const AnalysisUspMobile = () => {
  return (
    <Paper
      bg="#F3EDF8"
      className={classes.analysisContainerMobile}
      pr={rem(24)}
      py={rem(24)}
      hiddenFrom="lg"
    >
      <Group gap="xs" justify="flex-end">
        <Flex justify="flex-end">
          <CreatorCard isMobile />
        </Flex>
        <Flex gap="xs" justify="flex-end">
          <CreatorDataCard1 isMobile />
          <CreatorDataCard2 isMobile />
        </Flex>
      </Group>
    </Paper>
  );
};

const AnalysisUspDesktop = () => {
  return (
    <Paper
      bg="#F3EDF8"
      className={classes.analysisContainer}
      px={rem(48)}
      py={rem(40)}
      visibleFrom="lg"
    >
      <Group w="100%" gap="sm">
        <CreatorCard />
        <CreatorDataCard1 />
        <CreatorDataCard2 />
      </Group>
    </Paper>
  );
};

const AnalysisUsp = () => {
  return (
    <>
      <AnalysisUspDesktop />
      <AnalysisUspMobile />
    </>
  );
};

export default AnalysisUsp;
