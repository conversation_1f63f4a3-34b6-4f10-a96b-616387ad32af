import {
  skincareCreators,
  travelCreators,
} from "@/components/common/constants/constants";
import {
  Avatar,
  Box,
  Center,
  Flex,
  Group,
  Paper,
  rem,
  RingProgress,
  Skeleton,
  Stack,
  Text,
} from "@mantine/core";
import { IconHeart, IconMapPin } from "@tabler/icons-react";
import { useState } from "react";
import TypewriterComponent from "typewriter-effect";
import classes from "./UspSection.module.css";

const CreatorCard = ({
  showLoading,
  creator,
  isMobile,
}: {
  showLoading: boolean;
  creator: any;
  isMobile?: boolean;
}) => {
  if (showLoading) {
    return <CreatorDummyCard isMobile={isMobile} />;
  }
  return (
    <Paper
      withBorder
      p={isMobile ? "xs" : "md"}
      radius="md"
      w={isMobile ? rem(220) : rem(350)}
      miw={isMobile ? rem(220) : rem(350)}
      maw={isMobile ? rem(220) : rem(350)}
      style={{ borderColor: "#0000000F" }}
    >
      <Stack>
        <Flex justify="space-between">
          <Flex gap={isMobile ? rem(8) : "md"}>
            <Avatar
              src={creator.profilePic}
              radius="sm"
              size={isMobile ? rem(28) : rem(52)}
            />
            <Stack gap={isMobile ? rem(4) : rem(2)}>
              <Text size={isMobile ? rem(8) : "lg"} fw={500}>
                {creator.name}
              </Text>
              <Flex align="center" gap={rem(4)}>
                <IconMapPin
                  style={{
                    width: isMobile ? rem(8) : rem(12),
                    height: isMobile ? rem(8) : rem(12),
                    color: "#32323280",
                  }}
                />
                <Text c="#32323280" size={isMobile ? rem(8) : "sm"} mt={rem(2)}>
                  {creator.location}
                </Text>
              </Flex>
            </Stack>
          </Flex>
          <IconHeart
            style={{
              width: isMobile ? rem(10) : rem(18),
              height: isMobile ? rem(10) : rem(18),
              color: "#32323280",
              marginTop: rem(4),
            }}
            stroke={1.5}
          />
        </Flex>
        <Group gap={isMobile ? rem(8) : "xs"}>
          <Paper
            bg="#ECFDF3"
            c="#027A48"
            px={isMobile ? rem(6) : rem(10)}
            py={isMobile ? rem(2) : rem(4)}
            radius={isMobile ? rem(8) : rem(16)}
          >
            <Text size={isMobile ? rem(8) : "sm"}>{creator.match}</Text>
          </Paper>
          <Paper
            bg="#F4F3FF"
            c="#5925DC"
            px={isMobile ? rem(6) : rem(10)}
            py={isMobile ? rem(2) : rem(4)}
            radius={isMobile ? rem(8) : rem(16)}
          >
            <Text size={isMobile ? rem(8) : "sm"}>{creator.tag}</Text>
          </Paper>
          {creator.niche.map((niche: string, i: number) => (
            <Paper
              key={`creatorNiche-${i}`}
              bg="#F2F4F7"
              c="#344054"
              px={isMobile ? rem(6) : rem(10)}
              py={isMobile ? rem(2) : rem(4)}
              radius={isMobile ? rem(8) : rem(16)}
            >
              <Text size={isMobile ? rem(8) : "sm"}>{niche}</Text>
            </Paper>
          ))}
        </Group>
        <Group gap={isMobile ? rem(4) : "md"}>
          {creator.metrics.map((metric: any, i: number) => (
            <Paper
              bg="#F2F4F7"
              c="#344054"
              p={isMobile ? rem(6) : "xs"}
              radius={rem(8)}
            >
              <Stack gap={rem(4)}>
                <Flex justify="space-between" align="center" gap={rem(24)}>
                  <Text size={isMobile ? rem(6) : "sm"}>{metric.value}</Text>
                  <RingProgress
                    sections={[{ value: 70, color: "lightgreen" }]}
                    size={isMobile ? 14 : 24}
                    thickness={isMobile ? 2 : 3.5}
                  />
                </Flex>
                <Text size={isMobile ? rem(6) : "sm"}>{metric.key}</Text>
              </Stack>
            </Paper>
          ))}
        </Group>
        <Flex c="#393939" justify="space-between" align="center">
          <Text ta="center" w="50%" size={isMobile ? "xs" : "md"}>
            Starting ₹{creator.price}
          </Text>
          <Paper
            bg="#2F2F2F"
            c="#fff"
            ta="center"
            py="xs"
            w="50%"
            px={isMobile ? "xs" : rem(32)}
            radius={isMobile ? rem(22) : rem(40)}
          >
            <Text size={isMobile ? rem(10) : "md"}>View Portfolio</Text>
          </Paper>
        </Flex>
      </Stack>
    </Paper>
  );
};

const CreatorDummyCard = ({ isMobile }: { isMobile?: boolean }) => {
  return (
    <Paper
      withBorder
      style={{ borderColor: "#0000000F" }}
      p={isMobile ? "xs" : "md"}
      w={isMobile ? rem(220) : rem(350)}
      miw={isMobile ? rem(220) : rem(350)}
      maw={isMobile ? rem(220) : rem(350)}
      radius="md"
    >
      <Flex gap={isMobile ? "xs" : "md"}>
        <Skeleton width={isMobile ? 32 : 52} height={isMobile ? 32 : 52} />
        <Stack gap={isMobile ? 8 : "xs"} w="70%">
          <Skeleton width="60%" height={isMobile ? 16 : 20} />
          <Skeleton width="40%" height={isMobile ? 8 : 12} />
        </Stack>
        <Skeleton width={isMobile ? 16 : 20} height={isMobile ? 16 : 20} />
      </Flex>
      <Group mt="md" gap={isMobile ? 4 : rem(6)}>
        <Skeleton
          width={isMobile ? 60 : 80}
          height={isMobile ? 16 : 24}
          radius="lg"
        />
        <Skeleton
          width={isMobile ? 80 : 100}
          height={isMobile ? 16 : 24}
          radius="lg"
        />
        <Skeleton
          width={isMobile ? 60 : 80}
          height={isMobile ? 16 : 24}
          radius="lg"
        />
        <Skeleton
          width={isMobile ? 50 : 70}
          height={isMobile ? 16 : 24}
          radius="lg"
        />
        <Skeleton
          width={isMobile ? 70 : 90}
          height={isMobile ? 16 : 24}
          radius="lg"
        />
      </Group>
      <Group mt={isMobile ? "sm" : "lg"} gap={isMobile ? 4 : rem(6)}>
        <Skeleton
          width={isMobile ? 60 : 90}
          height={isMobile ? 40 : 70}
          radius="md"
        />
        <Skeleton
          width={isMobile ? 60 : 90}
          height={isMobile ? 40 : 70}
          radius="md"
        />
        <Skeleton
          width={isMobile ? 60 : 90}
          height={isMobile ? 40 : 70}
          radius="md"
        />
      </Group>
      <Group mt={isMobile ? "md" : "lg"} gap={rem(6)}>
        <Box w="48%">
          <Center>
            <Skeleton w="70%" height={isMobile ? 12 : 18} radius="md" />
          </Center>
        </Box>
        <Skeleton width="40%" height={isMobile ? 24 : 40} radius={rem(40)} />
      </Group>
    </Paper>
  );
};

const DiscoveryUspMobile = () => {
  const [showLoading, setShowLoading] = useState(true);
  const [creators, setCreators] = useState<any>(["", "", ""]);

  const setSkincareCreators = () => {
    setShowLoading(false);
    setCreators(skincareCreators);
  };

  const setTravelCreators = () => {
    setShowLoading(false);
    setCreators(travelCreators);
  };

  return (
    <Paper
      bg="#F3EDF8"
      className={classes.discoveryContainerMobile}
      py={rem(24)}
      pl={rem(24)}
      hiddenFrom="lg"
    >
      <Stack>
        <Flex gap="md">
          <Paper bg="#fff" px={rem(12)} py="xs" w="100%">
            <TypewriterComponent
              onInit={(typewriter) => {
                typewriter
                  .typeString("Skincare expert")
                  .callFunction(setSkincareCreators)
                  .pauseFor(4000)
                  .callFunction(() => setShowLoading(true))
                  .deleteAll()
                  .typeString("Travel influencers")
                  .callFunction(setTravelCreators)
                  .pauseFor(4000)
                  .callFunction(() => setShowLoading(true))
                  .start();
              }}
              options={{ loop: true }}
            />
          </Paper>
        </Flex>
      </Stack>

      <Flex mt="lg" gap="lg" maw="100%">
        {creators.map((creator: any, idx: number) => (
          <CreatorCard
            key={`creatorsCard-${idx}`}
            showLoading={showLoading}
            creator={creator}
            isMobile
          />
        ))}
      </Flex>
    </Paper>
  );
};

const DiscoveryUspDesktop = () => {
  const [showLoading, setShowLoading] = useState(true);
  const [creators, setCreators] = useState<any>(["", "", ""]);

  const setSkincareCreators = () => {
    setShowLoading(false);
    setCreators(skincareCreators);
  };

  const setTravelCreators = () => {
    setShowLoading(false);
    setCreators(travelCreators);
  };
  return (
    <Paper
      bg="#F3EDF8"
      className={classes.discoveryContainer}
      px={rem(48)}
      py={rem(40)}
      visibleFrom="lg"
    >
      <Stack>
        <Flex gap="md">
          <Paper bg="#fff" px={rem(24)} py="sm" radius="md" w="90%">
            <TypewriterComponent
              onInit={(typewriter) => {
                typewriter
                  .typeString("Skincare expert")
                  .callFunction(setSkincareCreators)
                  .pauseFor(4000)
                  .callFunction(() => setShowLoading(true))
                  .deleteAll()
                  .typeString("Travel influencers")
                  .callFunction(setTravelCreators)
                  .pauseFor(4000)
                  .callFunction(() => setShowLoading(true))
                  .start();
              }}
              options={{ loop: true }}
            />
          </Paper>
          <Paper
            bg="#000"
            px={rem(24)}
            py="sm"
            radius="md"
            c="#fff"
            visibleFrom="lg"
          >
            Search
          </Paper>
        </Flex>
      </Stack>

      <Flex mt="lg" gap="lg" maw="100%">
        {creators.map((creator: any, idx: number) => (
          <CreatorCard
            key={`creatorsCard-${idx}`}
            showLoading={showLoading}
            creator={creator}
          />
        ))}
      </Flex>
    </Paper>
  );
};

const DiscoveryUsp = () => {
  return (
    <>
      <DiscoveryUspDesktop />
      <DiscoveryUspMobile />
    </>
  );
};

export default DiscoveryUsp;
