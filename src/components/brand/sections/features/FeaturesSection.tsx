import { Flex, Image, Paper, rem, Stack, Text, Title } from "@mantine/core";

const FeaturesTitleMobile = () => {
  return (
    <Flex justify="center" hiddenFrom="lg">
      <Stack align="center" gap={rem(8)} ta="center">
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          Our Targeted Approach
        </Text>
        <Title order={1} size={rem(34)} fw={400} w="80%">
          Expressway to impactful, results-driven partnerships
        </Title>
        <Text
          fw={400}
          lh={rem(22)}
          size={rem(14)}
          c="#000000CC"
          ta="center"
          w="80%"
        >
          Our platform utilizes vetting tools that extract data-driven insights
          and simplify your task to search for a creator who’s ideal for you.
        </Text>
      </Stack>
    </Flex>
  );
};
const FeaturesTitleDesktop = () => {
  return (
    <Flex justify="center" visibleFrom="lg">
      <Stack align="center" gap={rem(8)}>
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          TAILORED ESPECIALLY FOR YOU
        </Text>
        <Title order={1} size={rem(36)} fw={400}>
          Expressway to impactful, results-driven partnerships
        </Title>
        <Text
          fw={400}
          lh={rem(25)}
          size={rem(16)}
          c="#000000CC"
          ta="center"
          w="60%"
        >
          Our platform utilizes vetting tools that extract data-driven insights
          and simplify your task to search for a creator who’s ideal for you.
          It’s time to lighten your load and bid goodbye to all the guesswork —
          partner with creators who drive you towards a result-oriented success.
        </Text>
      </Stack>
    </Flex>
  );
};

const FeaturesSection = () => {
  return (
    <Stack mt={{ base: "0", md: "10%" }}>
      <FeaturesTitleDesktop />
      <FeaturesTitleMobile />
      <Flex
        mx={{ base: rem(24), md: "10%" }}
        mt={rem(60)}
        gap="sm"
        mah={{ base: "100%", md: rem(420) }}
        direction={{ base: "column", md: "row" }}
      >
        <Paper
          withBorder
          radius={rem(18)}
          p="0"
          style={{ borderColor: "#E9E9E9" }}
          w={{ base: "100%", md: rem("49%") }}
        >
          <Image
            src="/brand-feature-1.png"
            style={{
              borderTopLeftRadius: rem(18),
              borderTopRightRadius: rem(18),
            }}
          />
          <Stack
            gap={rem(8)}
            p={rem(24)}
            pt={rem(12)}
            style={{ borderTop: "1px solid #0000001A" }}
          >
            <Text size={rem(20)} fw={400}>
              Unfiltered Representation
            </Text>
            <Text size={rem(14)} fw={400} c="#00000080" lh={rem(20)}>
              Genuine voices and perspectives create a reliable experience of
              trust based on authentic representation.
            </Text>
          </Stack>
        </Paper>
        <Paper
          withBorder
          radius={rem(18)}
          p="0"
          style={{ borderColor: "#E9E9E9" }}
          w={{ base: "100%", md: rem("49%") }}
        >
          <Image
            src="/brand-feature-2.png"
            style={{
              borderTopLeftRadius: rem(18),
              borderTopRightRadius: rem(18),
            }}
          />
          <Stack
            gap={rem(8)}
            p={rem(24)}
            pt={rem(12)}
            style={{ borderTop: "1px solid #0000001A" }}
          >
            <Text size={rem(20)} fw={400}>
              Meaningful Audience Interaction
            </Text>
            <Text size={rem(14)} fw={400} c="#00000080" lh={rem(20)}>
              Build trust by choosing creators who resonate with your idea of
              having a personalized interaction with the audience
            </Text>
          </Stack>
        </Paper>
        <Paper
          withBorder
          radius={rem(18)}
          p="0"
          style={{ borderColor: "#E9E9E9" }}
          w={{ base: "100%", md: rem("49%") }}
        >
          <Image
            src="/brand-feature-3.png"
            style={{
              borderTopLeftRadius: rem(18),
              borderTopRightRadius: rem(18),
            }}
          />
          <Stack
            gap={rem(8)}
            p={rem(24)}
            pt={rem(12)}
            style={{ borderTop: "1px solid #0000001A" }}
          >
            <Text size={rem(20)} fw={400}>
              Long-Lasting Brand Influence
            </Text>
            <Text size={rem(14)} fw={400} c="#00000080" lh={rem(20)}>
              Connects with creators who provide relevant content with
              consistency to leave a long term impact on the audience.
            </Text>
          </Stack>
        </Paper>
      </Flex>
    </Stack>
  );
};

export default FeaturesSection;
