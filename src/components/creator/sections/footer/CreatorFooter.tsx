import Button from "@/components/common/button/Button";
import { COLECTA_APP_SIGNUP_URL } from "@/components/common/constants/urls";
import { Box, Flex, rem, Stack, Text, Title } from "@mantine/core";
import Link from "next/link";
import classes from "./CreatorFooter.module.css";

const CreatorFooterMobile = () => {
  return (
    <Box className={classes.creatorFooterContainerMobile} hiddenFrom="lg">
      <Stack gap={rem(20)} justify="space-between" h="95%">
        <Stack align="flex-end">
          <Title
            order={1}
            size={rem(28)}
            fw={400}
            fs="italic"
            className={classes.creatorFooterTitle}
          >
            Ready to Collaborate?
          </Title>
          <Text size={rem(16)} fw={300} w={rem("75%")} lh={rem(27)} ta="right">
            With Colecta.ai, discover brands invested in meaningful, long-term
            partnerships that support your growth.
          </Text>
        </Stack>
        <Link
          style={{ width: "100%" }}
          href={COLECTA_APP_SIGNUP_URL}
          target="_blank"
        >
          <Button mt="xl" fs="italic" size="xl" w="90%">
            Sign Up as a Creator
          </Button>
        </Link>
      </Stack>
    </Box>
  );
};
const CreatorFooterDesktop = () => {
  return (
    <Box className={classes.creatorFooterContainer} visibleFrom="lg">
      <Stack gap={rem(20)}>
        <Title
          order={1}
          size={rem(56)}
          fw={400}
          fs="italic"
          className={classes.creatorFooterTitle}
          w={rem("50%")}
        >
          Ready to Build Lasting Brand Relationships?
        </Title>
        <Text size={rem(20)} fw={300} w={rem("45%")} lh={rem(32)}>
          With Colecta.ai, discover brands invested in meaningful, long-term
          partnerships that support your growth.
        </Text>
        <Flex w="100%" mt={rem(60)} pl={rem(300)}>
          <Link
            style={{ width: "100%" }}
            href={COLECTA_APP_SIGNUP_URL}
            target="_blank"
          >
            <Button mt="xl" fs="italic" size="xl" w="100%">
              Get Early Access
            </Button>
          </Link>
        </Flex>
      </Stack>
    </Box>
  );
};

const CreatorFooter = () => {
  return (
    <>
      <CreatorFooterDesktop />
      <CreatorFooterMobile />
    </>
  );
};

export default CreatorFooter;
