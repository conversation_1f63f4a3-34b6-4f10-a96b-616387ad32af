import { Flex, Image, Paper, rem, Stack, Text, Title } from "@mantine/core";

const FeaturesTitleMobile = () => {
  return (
    <Flex justify="center" hiddenFrom="lg">
      <Stack align="center" gap={rem(8)} ta="center">
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          Our Specialized Approach
        </Text>
        <Title order={1} size={rem(34)} fw={400} w="80%">
          Walk into your smart path to success-driven collaborations
        </Title>
        <Text
          fw={400}
          lh={rem(22)}
          size={rem(14)}
          c="#000000CC"
          ta="center"
          w="80%"
        >
          Finding befitting brands that relate with your unique voice and share
          your idea of growth is no more a hassle.
        </Text>
      </Stack>
    </Flex>
  );
};
const FeaturesTitleDesktop = () => {
  return (
    <Flex justify="center" visibleFrom="lg">
      <Stack align="center" gap={rem(8)}>
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          Our Specialized Approach
        </Text>
        <Title order={1} size={rem(36)} fw={400} tt="capitalize">
          Walk into your smart path to success-driven collaborations
        </Title>
        <Text
          fw={400}
          lh={rem(25)}
          size={rem(16)}
          c="#000000CC"
          ta="center"
          w="60%"
        >
          Finding befitting brands that relate with your unique voice and share
          your idea of growth is no more a hassle. With the intelligence of AI,
          Colecta.ai is the one-stop solution to analyzing and filtering the
          ones that resonate with your values.
        </Text>
      </Stack>
    </Flex>
  );
};

const FeaturesSection = () => {
  return (
    <Stack mt={{ base: "0", md: "10%" }}>
      <FeaturesTitleDesktop />
      <FeaturesTitleMobile />
      <Flex
        mx={{ base: rem(24), md: "10%" }}
        mt={{ base: rem(24), md: rem(60) }}
        gap="sm"
        mah={{ base: "100%", md: rem(420) }}
        direction={{ base: "column", md: "row" }}
      >
        <Paper
          withBorder
          radius={rem(18)}
          p="0"
          style={{ borderColor: "#E9E9E9" }}
          w={{ base: "100%", md: rem("49%") }}
        >
          <Image
            src="/creator-feature-1.png"
            style={{
              borderTopLeftRadius: rem(18),
              borderTopRightRadius: rem(18),
            }}
          />
          <Stack
            gap={rem(8)}
            p={rem(24)}
            pt={rem(12)}
            style={{ borderTop: "1px solid #0000001A" }}
          >
            <Text size={rem(20)} fw={400}>
              Attaining Precision With Efficiency
            </Text>
            <Text size={rem(14)} fw={400} c="#00000080" lh={rem(20)}>
              Say goodbye to irrelevant briefs. Get matched with campaigns
              tailored to your content and audience.
            </Text>
          </Stack>
        </Paper>
        <Paper
          withBorder
          radius={rem(18)}
          p="0"
          style={{ borderColor: "#E9E9E9" }}
          w={{ base: "100%", md: rem("49%") }}
        >
          <Image
            src="/creator-feature-2.png"
            style={{
              borderTopLeftRadius: rem(18),
              borderTopRightRadius: rem(18),
            }}
          />
          <Stack
            gap={rem(8)}
            p={rem(24)}
            pt={rem(12)}
            style={{ borderTop: "1px solid #0000001A" }}
          >
            <Text size={rem(20)} fw={400}>
              Authentic Alignments
            </Text>
            <Text size={rem(14)} fw={400} c="#00000080" lh={rem(20)}>
              Ensuring that with its upgraded data analysis and scrutiny, you
              find the authentic match to your needs.
            </Text>
          </Stack>
        </Paper>
        <Paper
          withBorder
          radius={rem(18)}
          p="0"
          style={{ borderColor: "#E9E9E9", overflow: "hidden" }}
          w={{ base: "100%", md: rem("49%") }}
        >
          <Image
            src="/creator-feature-3.png"
            style={{
              borderTopLeftRadius: rem(18),
              borderTopRightRadius: rem(18),
            }}
          />
          <Stack
            gap={rem(8)}
            p={rem(24)}
            pt={rem(12)}
            style={{ borderTop: "1px solid #0000001A" }}
          >
            <Text size={rem(20)} fw={400}>
              Performance Analytics
            </Text>
            <Text size={rem(14)} fw={400} c="#00000080" lh={rem(20)}>
              Know what works and what doesn’t. Get insights to pitch your value
              confidently to brands.
            </Text>
          </Stack>
        </Paper>
      </Flex>
    </Stack>
  );
};

export default FeaturesSection;
