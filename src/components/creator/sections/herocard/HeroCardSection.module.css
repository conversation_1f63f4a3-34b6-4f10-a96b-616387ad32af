.cardImageContainer {
  height: rem(360);
  border-radius: rem(24);
  border-color: #f9f8f8;
  background: #f6f7f0;
  padding: rem(16) rem(16) rem(24);
  transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  overflow: hidden;
}

.cardImages:hover {
  transform: scale(1.04);
}

.cardImage {
  height: 90%;
  border-radius: rem(16);
  border: 0.96px solid #0000001a;
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
}

.textDesc {
  visibility: hidden;
}

.cardImageContainer:hover {
  .cardImage {
    height: 70%;
  }

  .textDesc {
    visibility: visible;
  }
}

.cardImageContainerMobile {
  height: rem(360);
  border-radius: rem(8);
  border-color: #f9f8f8;
  background: #f6f7f0;
  padding: rem(8);
  transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  overflow: hidden;
}
