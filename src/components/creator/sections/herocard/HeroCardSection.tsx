import {
  Badge,
  Box,
  Grid,
  Image,
  Paper,
  rem,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import classes from "./HeroCardSection.module.css";

const HeroCard = ({
  url,
  title,
  desc,
}: {
  url: string;
  title: string;
  desc: string;
}) => {
  return (
    <Paper className={classes.cardImageContainer} withBorder>
      <Stack h="100%" gap="0">
        <Box className={classes.cardImage}>
          <Image src={url} h="100%" radius={rem(16)} />
        </Box>
        <Stack gap={rem(8)} px={rem(16)} pb={rem(24)} pt={rem(16)}>
          <Text size={rem(20)} fw={400}>
            {title}
          </Text>
          <Text
            size={rem(16)}
            fw={400}
            c="#00000080"
            lh={rem(24)}
            w="90%"
            className={classes.textDesc}
          >
            {desc}
          </Text>
        </Stack>
      </Stack>
    </Paper>
  );
};

const HeroCardMobile = ({
  url,
  title,
  desc,
}: {
  url: string;
  title: string;
  desc: string;
}) => {
  return (
    <Paper className={classes.cardImageContainerMobile} withBorder>
      <Stack h="100%" gap="0">
        <Box className={classes.cardImage}>
          <Image src={url} h="100%" radius={rem(8)} />
        </Box>
        <Stack gap={rem(8)} p={rem(8)}>
          <Text size={rem(14)} fw={400}>
            {title}
          </Text>
          <Text size={rem(12)} fw={400} c="#00000080" lh={rem(16)}>
            {desc}
          </Text>
        </Stack>
      </Stack>
    </Paper>
  );
};

const HeroCardSection = () => {
  return (
    <>
      <HeroCardSectionDesktop />
      <HeroCardSectionMobile />
    </>
  );
};

const HeroCardSectionDesktop = () => {
  return (
    <Paper
      mt="5%"
      mx="5%"
      withBorder
      radius={rem(72)}
      p={rem(96)}
      visibleFrom="lg"
    >
      <Stack align="center">
        <Badge color="#96C235" variant="outline" size="lg">
          <Text c="#96C235" fw={600} size="md">
            AI Powered Collaboration
          </Text>
        </Badge>
        <Title order={2} size={rem(46)} fw={400} w="50%" ta="center">
          Discover a Smarter Way to Connect with Brands
        </Title>
        <Text fw={400} size={rem(22)} w="55%" lh={rem(36)} ta="center">
          See how we simplify the process, allowing you to focus on building
          relationships that drive results.
        </Text>
      </Stack>
      <Grid mt={rem(64)} gutter="xl" grow>
        <Grid.Col span={5}>
          <HeroCard
            url="/audience-insights.png"
            title="Creator Metrics"
            desc="Track performance with insights on engagement, reach, and growth to showcase your value."
          />
        </Grid.Col>
        <Grid.Col span={6}>
          <HeroCard
            url="/creator-profile.png"
            title="Personalized Profile"
            desc="Build a standout profile that highlights your skills, niche, and achievements, tailored to attract the right opportunities."
          />
        </Grid.Col>
        <Grid.Col span={6}>
          <HeroCard
            url="/apply-collab.png"
            title="AI Pitching"
            desc="Let AI craft compelling pitches for brands, ensuring your unique strengths and ideas are showcased effectively."
          />
        </Grid.Col>
        <Grid.Col span={5}>
          <HeroCard
            url="/collab-invites.png"
            title="Collaboration Invites"
            desc="Receive direct invites from brands aligned with your expertise for top opportunities."
          />
        </Grid.Col>
      </Grid>
    </Paper>
  );
};
const HeroCardSectionMobile = () => {
  return (
    <Paper mt="5%" mx="5%" radius={rem(24)} p={rem(24)} hiddenFrom="lg">
      <Stack align="flex-start">
        <Badge color="#96C235" variant="outline" size="md">
          <Text c="#96C235" fw={600} size="sm">
            AI Powered Collaboration
          </Text>
        </Badge>
        <Title order={2} size={rem(26)} fw={400} w="90%">
          Discover a Smarter Way to Connect with Brands
        </Title>
        <Text fw={400} size={rem(14)} w="85%" lh={rem(20)}>
          See how we simplify the process, allowing you to focus on building
          relationships that drive results.
        </Text>
      </Stack>
      <Grid mt={rem(20)} gutter="xl" grow>
        <Grid.Col span={12}>
          <HeroCardMobile
            url="/audience-insights.png"
            title="Creator Metrics"
            desc="Track performance with insights on engagement, reach, and growth to showcase your value."
          />
        </Grid.Col>
        <Grid.Col span={12}>
          <HeroCardMobile
            url="/creator-profile.png"
            title="Personalized Profile"
            desc="Build a standout profile that highlights your skills, niche, and achievements, tailored to attract the right opportunities."
          />
        </Grid.Col>
      </Grid>
    </Paper>
  );
};

export default HeroCardSection;
