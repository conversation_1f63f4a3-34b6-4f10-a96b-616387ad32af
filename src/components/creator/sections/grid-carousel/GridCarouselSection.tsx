import Button from "@/components/common/button/Button";
import { collabPostings } from "@/components/common/constants/constants";
import { COLECTA_APP_SIGNUP_URL } from "@/components/common/constants/urls";
import { Carousel } from "@mantine/carousel";
import { Badge, Flex, Group, Paper, rem, Stack, Text } from "@mantine/core";
import {
  IconArrowRight,
  IconCircleCheckFilled,
  IconClockHour2,
} from "@tabler/icons-react";
import Autoplay from "embla-carousel-autoplay";
import Link from "next/link";
import { useRef } from "react";
import classes from "./GridCarouselSection.module.css";

const GridCarouselSectionMobile = () => {
  return (
    <Stack
      justify="space-between"
      align="flex-start"
      p={rem(24)}
      hiddenFrom="lg"
    >
      <Text
        size={rem(24)}
        fw={400}
        lh={rem(28)}
        fs="italic"
        className={classes.titleText}
      >
        Collaborations Curated Just for You
      </Text>
      <Text fw={400} size={rem(14)} w="90%" lh={rem(22)}>
        Sifting though numerous options to find the perfect brand that helps to
        amplify your voice
      </Text>
      <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
        <Button
          mt="md"
          fs="italic"
          size="lg"
          color="#373737"
          fw={500}
          rightSection={
            <IconArrowRight
              style={{
                width: rem(16),
                height: rem(16),
              }}
            />
          }
        >
          Look for brands
        </Button>
      </Link>
    </Stack>
  );
};
const GridCarouselSectionDesktop = () => {
  return (
    <Flex
      justify="space-between"
      align="flex-start"
      px={rem(112)}
      pb={rem(40)}
      visibleFrom="lg"
    >
      <Stack>
        <Text
          size={rem(40)}
          fw={400}
          lh={rem(48)}
          fs="italic"
          className={classes.titleText}
        >
          Collaborations Curated Just for You
        </Text>
        <Text fw={400} size={rem(20)} w="60%" lh={rem(32)}>
          Sifting though numerous options to find the perfect brand that helps
          to amplify your voice
        </Text>
      </Stack>
      <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
        <Button
          fs="italic"
          size="md"
          color="#373737"
          fw={500}
          rightSection={
            <IconArrowRight
              style={{
                width: rem(16),
                height: rem(16),
              }}
            />
          }
        >
          Look for brands
        </Button>
      </Link>
    </Flex>
  );
};

const CollabCard = ({ collab }: { collab: any }) => {
  return (
    <Paper
      withBorder
      p={rem(28)}
      radius={rem(12)}
      style={{ borderColor: "#0000000F" }}
      miw="80%"
      className={classes.creatorCard}
    >
      <Stack>
        <Flex justify="space-between" align="center">
          <Badge color="#96C235" variant="outline" size="sm" visibleFrom="lg">
            <Flex align="center">
              <Text fw={600}>{collab.match}% Match</Text>
            </Flex>
          </Badge>
          <Badge color="#96C235" variant="outline" size="xs" hiddenFrom="lg">
            <Flex align="center">
              <Text fw={600} size="xs">
                98% Match
              </Text>
            </Flex>
          </Badge>
        </Flex>

        <Stack gap={rem(8)}>
          <Text
            size={rem(16)}
            fw={500}
            c="#121212CC"
            lh={rem(22)}
            lineClamp={1}
          >
            {collab.title}
          </Text>
          <Text c="#32323299" size={rem(12)} lh={rem(22)} lineClamp={2}>
            {collab.description}
          </Text>
        </Stack>
        <Group gap={rem(4)}>
          <Paper
            bg="#F5F9FF"
            c="#1257BF"
            px={rem(10)}
            py={rem(4)}
            radius={rem(16)}
          >
            <Text size="sm">{collab.tags[0]}</Text>
          </Paper>
          <Paper
            bg="#FBF5FF"
            c="#8131B6"
            px={rem(10)}
            py={rem(4)}
            radius={rem(16)}
          >
            <Text size="sm">{collab.tags[1]}</Text>
          </Paper>
          <Paper
            bg="#F2F4F7"
            c="#344054"
            px={rem(10)}
            py={rem(4)}
            radius={rem(16)}
          >
            <Text size="sm">{collab.tags[2]}</Text>
          </Paper>
        </Group>
        <Flex justify="space-between" align="center">
          <Group>
            <Flex align="center" gap={rem(6)}>
              <IconCircleCheckFilled
                style={{ width: rem(18), height: rem(18) }}
                color="#3DA100"
              />
              <Text size={rem(14)} fw={500} c="#323232B2">
                Payment verified
              </Text>
            </Flex>
            <Text size={rem(14)} fw={500} c="#323232B2">
              Fixed Budget ₹ {collab.budget}
            </Text>
          </Group>

          <Flex align="center" gap={rem(6)} visibleFrom="lg">
            <IconClockHour2
              style={{ width: rem(16), height: rem(16) }}
              color="#323232B2"
            />
            <Text size={rem(14)} fw={500} c="#323232B2">
              Posted {collab.posted}
            </Text>
          </Flex>
        </Flex>
      </Stack>
    </Paper>
  );
};

const GridCarouselSection = () => {
  const autoplay = useRef(Autoplay({ delay: 1500 }));

  return (
    <Paper mb="8%" py={{ base: rem(32), md: rem(76) }} bg="#EDEEE280">
      <GridCarouselSectionDesktop />
      <GridCarouselSectionMobile />
      <Carousel
        slideGap={{ base: "xs", sm: "md" }}
        slideSize={{ base: "95%", md: "40%" }}
        loop
        withControls={false}
        plugins={[autoplay.current]}
      >
        {collabPostings.map((collab: any, idx: number) => (
          <Carousel.Slide key={`collabCard-${idx}`}>
            <CollabCard collab={collab} />
          </Carousel.Slide>
        ))}
      </Carousel>
    </Paper>
  );
};

export default GridCarouselSection;
