import {
  Badge,
  Flex,
  Group,
  Paper,
  rem,
  Stack,
  Text,
  Transition,
} from "@mantine/core";
import { useInViewport } from "@mantine/hooks";
import {
  IconCheck,
  IconCircleCheckFilled,
  IconClockHour2,
} from "@tabler/icons-react";
import CountUp from "react-countup";
import classes from "./UspSection.module.css";

const CreatorCardMobile = ({
  showAccept = false,
}: {
  showAccept?: boolean;
}) => {
  const { ref, inViewport } = useInViewport();

  return (
    <Paper
      withBorder
      p={rem(20)}
      radius={rem(16)}
      style={{ borderColor: "#0000000F" }}
      miw="105%"
      className={classes.creatorCard}
      ref={ref}
    >
      <Stack>
        <Flex justify="space-between" align="center">
          <Badge color="#96C235" variant="outline" size="sm" visibleFrom="lg">
            <Flex align="center">
              <CountUp start={10} end={98} duration={5} redraw={inViewport} />
              <Text fw={600}>% Match</Text>
            </Flex>
          </Badge>
          <Badge color="#96C235" variant="outline" size="sm" hiddenFrom="lg">
            <Flex align="center">
              <CountUp start={10} end={98} duration={5} redraw={inViewport} />
              <Text fw={600} size="xs">
                % Match
              </Text>
            </Flex>
          </Badge>
          <Transition
            mounted={showAccept && inViewport}
            transition="pop-top-right"
            duration={400}
            enterDelay={800}
            timingFunction="ease"
          >
            {(styles) => (
              <Badge
                color="#000"
                size="sm"
                style={styles}
                leftSection={
                  <IconCheck style={{ width: rem(12), height: rem(12) }} />
                }
              >
                <Text fw={600} size="xs">
                  Accept
                </Text>
              </Badge>
            )}
          </Transition>
        </Flex>

        <Stack gap={rem(8)}>
          <Text size={rem(16)} fw={500} c="#121212CC" lh={rem(22)}>
            Looking out for travel influencers to create awareness for our brand
          </Text>
          <Text c="#32323299" size={rem(12)} fw={400} lh={rem(20)}>
            Looking for an intermediate to experienced video editor for a career
            coach's YouTube channel. The edits would be fairly simple, most
          </Text>
        </Stack>
        <Group gap={rem(12)}>
          <Paper
            bg="#F5F9FF"
            c="#1257BF"
            px={rem(8)}
            py={rem(4)}
            radius={rem(8)}
          >
            <Text size="xs">Cosmetics</Text>
          </Paper>
          <Transition
            mounted={inViewport}
            transition="pop"
            duration={200}
            enterDelay={400}
            timingFunction="ease"
          >
            {(styles) => (
              <Paper
                bg="#FBF5FF"
                c="#8131B6"
                px={rem(8)}
                py={rem(4)}
                radius={rem(8)}
                style={styles}
              >
                <Text size="xs">Beauty & Cosmetics</Text>
              </Paper>
            )}
          </Transition>

          <Transition
            mounted={inViewport}
            transition="pop"
            duration={200}
            enterDelay={600}
            timingFunction="ease"
          >
            {(styles) => (
              <Paper
                bg="#F2F4F7"
                c="#344054"
                px={rem(8)}
                py={rem(4)}
                radius={rem(8)}
                style={styles}
              >
                <Text size="xs">Skincare</Text>
              </Paper>
            )}
          </Transition>
        </Group>
        <Flex justify="space-between" align="center">
          <Group>
            <Flex align="center" gap={rem(6)}>
              <IconCircleCheckFilled
                style={{ width: rem(14), height: rem(14) }}
                color="#3DA100"
              />
              <Text size={rem(12)} fw={500} c="#323232B2">
                Payment verified
              </Text>
            </Flex>
            <Text size={rem(12)} fw={500} c="#323232B2">
              Fixed Budget ₹ 56,000
            </Text>
          </Group>
        </Flex>
      </Stack>
    </Paper>
  );
};

const CreatorCard = ({ showAccept = false }: { showAccept?: boolean }) => {
  const { ref, inViewport } = useInViewport();

  return (
    <Paper
      withBorder
      p={rem(28)}
      radius={rem(12)}
      style={{ borderColor: "#0000000F" }}
      miw="80%"
      className={classes.creatorCard}
      ref={ref}
    >
      <Stack>
        <Flex justify="space-between" align="center">
          <Badge color="#96C235" variant="outline" size="lg">
            <Flex align="center">
              <CountUp start={10} end={98} duration={5} redraw={inViewport} />
              <Text fw={600}>% Match</Text>
            </Flex>
          </Badge>
          <Transition
            mounted={showAccept && inViewport}
            transition="pop-top-right"
            duration={400}
            enterDelay={800}
            timingFunction="ease"
          >
            {(styles) => (
              <Badge
                color="#000"
                size="lg"
                style={styles}
                leftSection={
                  <IconCheck style={{ width: rem(16), height: rem(16) }} />
                }
              >
                <Text fw={600} size="sm">
                  Accept
                </Text>
              </Badge>
            )}
          </Transition>
        </Flex>

        <Stack gap={rem(8)}>
          <Text size={rem(24)} fw={500} c="#121212CC" lh={rem(34)}>
            Looking out for travel influencers to create awareness for our brand
          </Text>
          <Text c="#32323299" size={rem(16)} lh={rem(30)}>
            Looking for an intermediate to experienced video editor for a career
            coach's YouTube channel. The edits would be fairly simple, most
          </Text>
        </Stack>
        <Group>
          <Paper
            bg="#F5F9FF"
            c="#1257BF"
            px={rem(10)}
            py={rem(4)}
            radius={rem(16)}
          >
            <Text size="sm">Cosmetics</Text>
          </Paper>
          <Transition
            mounted={inViewport}
            transition="pop"
            duration={200}
            enterDelay={400}
            timingFunction="ease"
          >
            {(styles) => (
              <Paper
                bg="#FBF5FF"
                c="#8131B6"
                px={rem(10)}
                py={rem(4)}
                radius={rem(16)}
                style={styles}
              >
                <Text size="sm">Beauty & Cosmetics</Text>
              </Paper>
            )}
          </Transition>

          <Transition
            mounted={inViewport}
            transition="pop"
            duration={200}
            enterDelay={600}
            timingFunction="ease"
          >
            {(styles) => (
              <Paper
                bg="#F2F4F7"
                c="#344054"
                px={rem(10)}
                py={rem(4)}
                radius={rem(16)}
                style={styles}
              >
                <Text size="sm">Skincare</Text>
              </Paper>
            )}
          </Transition>
        </Group>
        <Flex justify="space-between" align="center">
          <Group>
            <Flex align="center" gap={rem(6)}>
              <IconCircleCheckFilled
                style={{ width: rem(18), height: rem(18) }}
                color="#3DA100"
              />
              <Text size={rem(14)} fw={500} c="#323232B2">
                Payment verified
              </Text>
            </Flex>
            <Text size={rem(14)} fw={500} c="#323232B2">
              Fixed Budget ₹ 56,000
            </Text>
          </Group>

          <Flex align="center" gap={rem(6)}>
            <IconClockHour2
              style={{ width: rem(16), height: rem(16) }}
              color="#323232B2"
            />
            <Text size={rem(14)} fw={500} c="#323232B2">
              Posted 2 hours ago
            </Text>
          </Flex>
        </Flex>
      </Stack>
    </Paper>
  );
};

const FindPerfectFitMobile = () => {
  return (
    <Paper
      bg="#E8ECDC99"
      className={classes.analysisContainerMobile}
      pr={rem(12)}
      py={rem(12)}
      hiddenFrom="lg"
    >
      <Flex gap="lg" maw="100%" direction="row-reverse">
        <CreatorCardMobile showAccept />
        <CreatorCardMobile />
      </Flex>
    </Paper>
  );
};
const FindPerfectFitDesktop = () => {
  return (
    <Paper
      bg="#E8ECDC99"
      className={classes.analysisContainer}
      pr={rem(48)}
      py={rem(40)}
      visibleFrom="lg"
    >
      <Flex gap="lg" maw="100%" direction="row-reverse">
        <CreatorCard showAccept />
        <CreatorCard />
      </Flex>
    </Paper>
  );
};

const FindPerfectFit = () => {
  return (
    <>
      <FindPerfectFitDesktop />
      <FindPerfectFitMobile />
    </>
  );
};

export default FindPerfectFit;
