import Button from "@/components/common/button/Button";
import { COLECTA_APP_SIGNUP_URL } from "@/components/common/constants/urls";
import { Flex, Grid, rem, Stack, Text, Title } from "@mantine/core";
import { IconArrowRight } from "@tabler/icons-react";
import Link from "next/link";
import FindPerfectFit from "./FindPerfectFit";
import InsightsUsp from "./InsightsUsp";

const UspSectionTitleMobile = () => {
  return (
    <Flex justify="center" hiddenFrom="lg">
      <Stack align="center" gap={rem(8)} ta="center">
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          Finding the Right Fit
        </Text>
        <Title order={1} size={rem(34)} fw={400} w="80%">
          Not Just An Ordinary Endorsement
        </Title>
        <Text
          fw={400}
          lh={rem(22)}
          size={rem(14)}
          c="#000000CC"
          ta="center"
          w="90%"
        >
          Colecta.ai connects you with brands that appreciate your voice, value
          your unique ideas, understand your needs and empower you to make a
          genuine impact
        </Text>
        <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
          <Button
            mt="lg"
            fs="italic"
            size="lg"
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
                stroke={1.5}
              />
            }
          >
            Explore Opportunities
          </Button>
        </Link>
      </Stack>
    </Flex>
  );
};
const UspSectionTitleDesktop = () => {
  return (
    <Flex justify="center" visibleFrom="lg">
      <Stack align="center" gap={rem(8)}>
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          Finding the Right Fit
        </Text>
        <Title order={1} size={rem(36)} fw={400} w="50%" ta="center">
          Not Just An Ordinary Endorsement- Nurturing True Partnerships With
          Brands
        </Title>
        <Text
          fw={400}
          lh={rem(25)}
          size={rem(16)}
          c="#000000CC"
          ta="center"
          w="60%"
        >
          Colecta.ai connects you with brands that appreciate your voice, value
          your unique ideas, understand your needs and empower you to make a
          genuine impact. Encouraging lasting partnerships with brands that
          listen, support and grow with you!
        </Text>
        <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
          <Button
            mt="xl"
            fs="italic"
            size="md"
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
                stroke={1.5}
              />
            }
          >
            Explore Opportunities
          </Button>
        </Link>
      </Stack>
    </Flex>
  );
};

const UspSection = () => {
  return (
    <Stack mt="10%">
      <UspSectionTitleDesktop />
      <UspSectionTitleMobile />
      <Grid
        gutter={{ base: rem(36), md: rem(120) }}
        overflow="hidden"
        mt={{ base: rem(90), md: rem(120) }}
      >
        <Grid.Col span={7} visibleFrom="lg">
          <FindPerfectFit />
        </Grid.Col>
        <Grid.Col span={{ base: 12, md: 5 }}>
          {/* Desktop view */}
          <Stack mr={rem("20%")} mt={rem("12%")} visibleFrom="lg">
            <Title order={2} size={rem(28)} fw={400}>
              Effortless AI-Assisted Brand Discovery
            </Title>
            <Text size={rem(18)} fw={400} lh={rem(32)} c="#00000080">
              AI-powered tools use advanced algorithms to analyze multiple data
              sets like the market trends, brand attributes and user inputs. AI
              recognizes the brands that align which share your preferences,
              goals and values, ensuring organic as well as impactful
              collaborations.
            </Text>
          </Stack>
          {/* Mobile view */}
          <Stack ml={rem(24)} hiddenFrom="lg">
            <Title order={2} size={rem(22)} fw={400} w="90%">
              Effortless AI-Assisted Brand Discovery
            </Title>
            <Text size={rem(14)} fw={400} lh={rem(22)} c="#00000080" w="90%">
              AI-powered tools use advanced algorithms to analyze multiple data
              sets like the market trends, brand attributes and user inputs. AI
              recognizes the brands that align which share your preferences,
              goals and values, ensuring organic as well as impactful
              collaborations.
            </Text>
          </Stack>
        </Grid.Col>
        <Grid.Col span={11} hiddenFrom="lg">
          <FindPerfectFit />
        </Grid.Col>
      </Grid>
      <Grid
        gutter={{ base: rem(36), md: rem(120) }}
        overflow="hidden"
        mt={{ base: rem(90), md: rem(120) }}
      >
        <Grid.Col span={{ base: 12, md: 5 }}>
          {/* Desktop view */}
          <Stack ml={rem("20%")} mt={rem("12%")} visibleFrom="lg">
            <Title order={2} size={rem(28)} fw={400}>
              Insights That Empower You
            </Title>
            <Text size={rem(18)} fw={400} lh={rem(32)} c="#00000080">
              Stay ahead with real-time analytics and performance metrics
              designed to optimize your collaborations. Gain valuable insights
              into engagement, audience reach, and campaign impact—all in one
              place. Plus, enjoy the confidence of timely payments for every
              project you deliver.
            </Text>
          </Stack>
          {/* Mobile view */}
          <Stack mr={rem(24)} hiddenFrom="lg" align="flex-end" ta="right">
            <Title order={2} size={rem(22)} fw={400}>
              Insights That Empower You
            </Title>
            <Text size={rem(14)} fw={400} lh={rem(22)} c="#00000080" w="90%">
              Stay ahead with real-time analytics and performance metrics
              designed to optimize your collaborations. Gain valuable insights
              into engagement, audience reach, and campaign impact—all in one
              place.
            </Text>
          </Stack>
        </Grid.Col>
        <Grid.Col span={{ base: 11, md: 7 }} offset={{ base: 1, md: 0 }}>
          <InsightsUsp />
        </Grid.Col>
      </Grid>
    </Stack>
  );
};

export default UspSection;
