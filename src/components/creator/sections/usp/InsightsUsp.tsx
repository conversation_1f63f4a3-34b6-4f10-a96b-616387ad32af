import {
  Badge,
  ColorSwatch,
  Flex,
  Paper,
  rem,
  RingProgress,
  SimpleGrid,
  Stack,
  Text,
  Transition,
} from "@mantine/core";
import { useInViewport } from "@mantine/hooks";
import {
  IconCurrencyRupee,
  IconTrendingDown,
  IconTrendingUp,
  IconWaveSquare,
} from "@tabler/icons-react";
import { forwardRef } from "react";
import CountUp from "react-countup";
import classes from "./UspSection.module.css";

const MetricsCard = ({
  title,
  metric,
  metricSuffix,
  isMetricAnimated,
  badge,
  badgeDelay,
  countUpStart = 0,
  inViewport = false,
  isMobile = false,
}: {
  title: string;
  metric: number;
  metricSuffix?: string;
  isMetricAnimated?: boolean;
  badge: string;
  badgeDelay: number;
  countUpStart?: number;
  inViewport: boolean;
  isMobile?: boolean;
}) => {
  return (
    <Paper
      radius={isMobile ? rem(12) : rem(16)}
      p={rem(16)}
      h={isMobile ? rem(100) : rem(140)}
    >
      <Stack gap={isMobile ? rem(10) : rem(16)}>
        <Text size={isMobile ? rem(12) : rem(14)} fw={400} c="#2E2E2E">
          {title}
        </Text>
        {isMetricAnimated && (
          <Flex align="center">
            <CountUp
              start={countUpStart}
              decimals={1}
              end={metric}
              duration={5}
              redraw={inViewport}
              style={{
                fontSize: isMobile ? rem(18) : rem(28),
                fontWeight: 500,
                color: "#414141",
                lineHeight: isMobile ? rem(24) : rem(36),
              }}
            />
            <Text
              size={isMobile ? rem(18) : rem(28)}
              fw={500}
              c="#414141"
              lh={isMobile ? rem(24) : rem(36)}
            >
              {metricSuffix}
            </Text>
          </Flex>
        )}
        {!isMetricAnimated && (
          <Text
            size={isMobile ? rem(18) : rem(28)}
            fw={500}
            c="#414141"
            lh={rem(36)}
          >
            {metric}
            {metricSuffix}
          </Text>
        )}
        <Transition
          mounted={inViewport}
          transition="pop-top-right"
          duration={400}
          enterDelay={badgeDelay}
          timingFunction="ease"
        >
          {(styles) => (
            <Badge
              color={
                badge === "low"
                  ? "#C10A0A26"
                  : badge === "high"
                  ? "#23C10A26"
                  : "#C1990A26"
              }
              c={
                badge === "low"
                  ? "#C71026"
                  : badge === "high"
                  ? "#0B8A00"
                  : "#8A6C00"
              }
              size={isMobile ? "xs" : "sm"}
              style={styles}
              leftSection={
                badge === "mid" ? (
                  <IconWaveSquare style={{ width: rem(14), height: rem(14) }} />
                ) : badge === "low" ? (
                  <IconTrendingDown
                    style={{ width: rem(14), height: rem(14) }}
                  />
                ) : (
                  <IconTrendingUp style={{ width: rem(14), height: rem(14) }} />
                )
              }
            >
              <Text fw={500} size={isMobile ? "xs" : "sm"} tt="capitalize">
                {badge}
              </Text>
            </Badge>
          )}
        </Transition>
      </Stack>
    </Paper>
  );
};

const EarningsCard = ({ inViewport }: { inViewport: boolean }) => {
  return (
    <Paper radius={rem(16)} p={rem(16)} w="100%">
      <Stack gap={rem(16)}>
        <Text fw={400} c="#2E2E2E">
          Campaign Earnings
        </Text>
        <Stack gap="xs">
          <Flex align="center">
            <IconCurrencyRupee
              style={{ width: rem(28), height: rem(28), color: "#414141" }}
              fontWeight={500}
            />
            <Text size={rem(28)} fw={500}>
              <CountUp
                start={10000}
                end={526200}
                useIndianSeparators
                duration={5}
                redraw={inViewport}
                style={{
                  fontSize: rem(28),
                  fontWeight: 500,
                  color: "#414141",
                  lineHeight: rem(36),
                }}
              />
            </Text>
          </Flex>
          <Text size="lg" fw={500} c="#00000099">
            This Month
          </Text>
        </Stack>
        <Flex justify="space-between" align="flex-start">
          <RingProgress
            sections={[
              { value: 30, color: "#86CEEA", tooltip: "Youtube - 1,57,860" },
              { value: 20, color: "#FBA27C", tooltip: "Twitter - 1,05,240" },
              { value: 50, color: "#BB7CFB", tooltip: "Instagram - 2,63,100" },
            ]}
            size={145}
            thickness={12}
            transitionDuration={2000}
          />
          <Stack gap="xs">
            <Text c="#A39992" fw={400}>
              Channel
            </Text>
            <Stack gap={rem(4)}>
              <Flex align="center" gap="xs">
                <ColorSwatch color="#BB7CFB" size={14} radius={rem(4)} />
                <Text>Instagram</Text>
              </Flex>
              <Flex align="center" gap="xs">
                <ColorSwatch color="#86CEEA" size={14} radius={rem(4)} />
                <Text>Youtube</Text>
              </Flex>
              <Flex align="center" gap="xs">
                <ColorSwatch color="#FBA27C" size={14} radius={rem(4)} />
                <Text>Twitter</Text>
              </Flex>
            </Stack>
          </Stack>
        </Flex>
      </Stack>
    </Paper>
  );
};

const InsightsUspMobile = forwardRef<HTMLDivElement, any>(
  ({ inViewport }, ref) => {
    return (
      <Paper
        bg="#E8ECDC99"
        className={classes.discoveryContainerMobile}
        py={rem(16)}
        pl={rem(16)}
        ref={ref}
        hiddenFrom="lg"
      >
        <Flex gap={rem(24)} maw="100%">
          <SimpleGrid
            cols={2}
            miw="60%"
            spacing={rem(12)}
            verticalSpacing={rem(12)}
          >
            <MetricsCard
              title="Total Audience Reach"
              metric={425.3}
              countUpStart={100}
              isMetricAnimated
              metricSuffix="K"
              badge="low"
              badgeDelay={0}
              inViewport={inViewport}
              isMobile
            />
            <MetricsCard
              title="Average Engagement Rate"
              metric={12.5}
              metricSuffix="%"
              isMetricAnimated
              badge="high"
              badgeDelay={500}
              inViewport={inViewport}
              isMobile
            />
            <MetricsCard
              title="Sentiment Analysis"
              metric={3.2}
              metricSuffix="/5"
              isMetricAnimated
              badge="mid"
              badgeDelay={1000}
              inViewport={inViewport}
              isMobile
            />
            <MetricsCard
              title="Success Rate"
              metric={92}
              countUpStart={10}
              isMetricAnimated
              metricSuffix="%"
              badge="high"
              badgeDelay={1500}
              inViewport={inViewport}
              isMobile
            />
          </SimpleGrid>
        </Flex>
      </Paper>
    );
  }
);

const InsightsUspDesktop = forwardRef<HTMLDivElement, any>(
  ({ inViewport }, ref) => {
    return (
      <Paper
        bg="#E8ECDC99"
        className={classes.discoveryContainer}
        py={rem(48)}
        pl={rem(48)}
        ref={ref}
        visibleFrom="lg"
      >
        <Flex gap={rem(24)} maw="100%">
          <SimpleGrid
            cols={2}
            miw="60%"
            spacing={rem(24)}
            verticalSpacing={rem(24)}
          >
            <MetricsCard
              title="Total Audience Reach"
              metric={425.3}
              countUpStart={100}
              isMetricAnimated
              metricSuffix="K"
              badge="low"
              badgeDelay={0}
              inViewport={inViewport}
            />
            <MetricsCard
              title="Average Engagement Rate"
              metric={12.5}
              metricSuffix="%"
              isMetricAnimated
              badge="high"
              badgeDelay={500}
              inViewport={inViewport}
            />
            <MetricsCard
              title="Sentiment Analysis"
              metric={3.2}
              metricSuffix="/5"
              isMetricAnimated
              badge="mid"
              badgeDelay={1000}
              inViewport={inViewport}
            />
            <MetricsCard
              title="Success Rate"
              metric={92}
              countUpStart={10}
              isMetricAnimated
              metricSuffix="%"
              badge="high"
              badgeDelay={1500}
              inViewport={inViewport}
            />
          </SimpleGrid>
          <EarningsCard inViewport />
        </Flex>
      </Paper>
    );
  }
);

const InsightsUsp = () => {
  const { ref, inViewport } = useInViewport();
  return (
    <>
      <InsightsUspDesktop ref={ref} inViewport={inViewport} />
      <InsightsUspMobile ref={ref} inViewport={inViewport} />
    </>
  );
};

export default InsightsUsp;
