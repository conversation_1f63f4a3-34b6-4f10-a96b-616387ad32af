import {
  Flex,
  Image,
  ImageProps,
  Indicator,
  IndicatorProps,
  Paper,
  rem,
  RingProgress,
  Stack,
  Text,
} from "@mantine/core";
import { IconCircleCheckFilled, IconStarFilled } from "@tabler/icons-react";
import {
  MouseParallaxChild,
  MouseParallaxContainer,
} from "react-parallax-mouse";
import classes from "./HeroSection.module.css";

const ImageWithIndicator = ({
  url,
  isMobile,
  labelContent,
  imageProps,
  indicatorProps,
}: {
  url: string;
  isMobile?: boolean;
  labelContent?: React.ReactNode;
  imageProps?: ImageProps;
  indicatorProps?: IndicatorProps;
}) => {
  const Label = () => (
    <Paper
      px="sm"
      py={rem(4)}
      withBorder
      c="#000"
      style={{ borderColor: "#0000001F" }}
      radius={rem(12)}
    >
      {labelContent}
    </Paper>
  );
  return isMobile ? (
    <></>
  ) : (
    <Indicator label={<Label />} color="transparent" {...indicatorProps}>
      <Image src={url} {...imageProps} />
    </Indicator>
  );
};

const HeroImagesSection = ({ isMobile }: { isMobile?: boolean }) => {
  return (
    <MouseParallaxContainer
      globalFactorX={0.1}
      globalFactorY={0.1}
      className={classes.heroSectionContainer}
      resetOnLeave
    >
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.images}
        style={{
          top: "40%",
          left: "58.5%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-1.png"
          imageProps={{ w: rem(120) }}
          indicatorProps={{ position: "top-end" }}
          isMobile={isMobile}
          labelContent={
            <Stack gap={rem(2)} w={rem(90)}>
              <Flex gap="xs" align="center">
                <RingProgress
                  sections={[
                    { value: 37, color: "#3BB308", tooltip: "Skincare" },
                  ]}
                  size={30}
                  thickness={4}
                />
                <Text fw={600}>3:1</Text>
              </Flex>
              <Text fw={400} ml={rem(4)}>
                ROI
              </Text>
            </Stack>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.images}
        style={{
          top: "40%",
          left: "58.5%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-2.png"
          imageProps={{ w: rem(80) }}
          indicatorProps={{ position: "bottom-end", offset: -1 }}
          isMobile={!isMobile}
          labelContent={
            <Stack gap={rem(2)}>
              <Flex gap="xs" align="center">
                <Text fw={500} size="sm" c="#0B8A00">
                  Fixed budget $
                </Text>
              </Flex>
            </Stack>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.images}
        style={{
          top: "20%",
          left: "38.5%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-1.png"
          imageProps={{ w: rem(80) }}
          indicatorProps={{ position: "top-end", offset: -5 }}
          isMobile={!isMobile}
          labelContent={
            <Stack gap={rem(2)}>
              <Flex gap="xs" align="center">
                <Text fw={500} size="sm" c="#0B8A00">
                  Food
                </Text>
              </Flex>
            </Stack>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.3}
        factorY={0.2}
        className={classes.images}
        style={{
          top: "58%",
          left: "75%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-2.png"
          isMobile={isMobile}
          imageProps={{ w: rem(150) }}
          indicatorProps={{ position: "top-end" }}
          labelContent={
            <Flex align="center" gap={rem(6)}>
              <IconCircleCheckFilled
                style={{ width: rem(18), height: rem(18) }}
                color="#3DA100"
              />
              <Text size={rem(14)} fw={500} c="#323232B2">
                Payment verified
              </Text>
            </Flex>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.5}
        factorY={0.4}
        className={classes.images}
        style={{
          top: "12.5%",
          left: "72.8%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-3.png"
          isMobile={isMobile}
          imageProps={{ w: rem(130) }}
          indicatorProps={{ position: "bottom-end" }}
          labelContent={
            <Text fw={500} c="#0B8A00">
              82% Match
            </Text>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.3}
        factorY={0.5}
        className={classes.images}
        style={{
          top: "8.5%",
          left: "79%",
          zIndex: 0,
        }}
      >
        {isMobile ? <Image src="/brands/brands-3.png" w={rem(90)} /> : <></>}
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.1}
        factorY={0.5}
        className={classes.images}
        style={{
          top: "31.75%",
          left: "88%",
        }}
      >
        <ImageWithIndicator
          isMobile={isMobile}
          url="/brands/brands-5.png"
          imageProps={{ w: rem(62) }}
          indicatorProps={{ position: "top-end", offset: -6 }}
          labelContent={
            <Flex align="center" gap={rem(4)}>
              <IconStarFilled
                color="#FFBA09"
                style={{ width: rem(14), height: rem(14) }}
              />
              <Text fw={400} size="sm">
                4.5
              </Text>
            </Flex>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.3}
        factorY={0.6}
        className={classes.images}
        style={{
          top: "46%",
          left: "42%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-6.png"
          isMobile={isMobile}
          imageProps={{ w: rem(152) }}
          indicatorProps={{ position: "bottom-end" }}
          labelContent={
            <Stack gap={rem(2)}>
              <Flex gap="xs" align="center">
                <Text fw={500} size="sm" c="#0B8A00">
                  Fixed budget $
                </Text>
              </Flex>
            </Stack>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.4}
        factorY={0.3}
        className={classes.images}
        style={{
          top: "28.5%",
          left: "24%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-7.png"
          imageProps={{ w: rem(94) }}
          isMobile={isMobile}
          indicatorProps={{ position: "bottom-end" }}
          labelContent={
            <Text fw={500} c="#0B8A00">
              78% Match
            </Text>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.6}
        factorY={0.1}
        className={classes.images}
        style={{
          top: "25.5%",
          left: "9%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-8.png"
          imageProps={{ w: rem(64) }}
          indicatorProps={{ position: "bottom-start", offset: -6 }}
          labelContent={
            <Flex align="center" gap={rem(4)}>
              <IconStarFilled
                color="#FFBA09"
                style={{ width: rem(14), height: rem(14) }}
              />
              <Text fw={400} size="sm">
                4.8
              </Text>
            </Flex>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.5}
        factorY={0.3}
        className={classes.images}
        style={{
          top: "48%",
          left: "14%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-9.png"
          imageProps={{ w: rem(135) }}
          isMobile={isMobile}
          indicatorProps={{ position: "bottom-start", offset: -6 }}
          labelContent={
            <Flex align="center" gap={rem(4)}>
              <Paper
                c="#5925DC"
                bg="#F4F3FF"
                py={rem(2)}
                px="sm"
                my={rem(4)}
                radius={rem(16)}
              >
                Fashion
              </Paper>
              <Paper
                c="#B42318"
                bg="#FEF3F2"
                py={rem(2)}
                px="sm"
                my={rem(4)}
                radius={rem(16)}
              >
                Accessories
              </Paper>
            </Flex>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.5}
        factorY={0.3}
        className={classes.images}
        style={{
          top: "55%",
          left: "20%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-5.png"
          imageProps={{ w: rem(65) }}
          isMobile={!isMobile}
          indicatorProps={{ position: "bottom-start", offset: -6 }}
          labelContent={
            <Flex align="center" gap={rem(4)}>
              <Paper
                c="#5925DC"
                bg="#F4F3FF"
                py={rem(2)}
                px="sm"
                my={rem(4)}
                radius={rem(16)}
              >
                Clothing
              </Paper>
              <Paper
                c="#B42318"
                bg="#FEF3F2"
                py={rem(2)}
                px="sm"
                my={rem(4)}
                radius={rem(16)}
              >
                Fashion
              </Paper>
            </Flex>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.5}
        factorY={0.3}
        className={classes.images}
        style={{
          top: "75%",
          left: "50%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-9.png"
          imageProps={{ w: rem(120) }}
          isMobile={!isMobile}
          indicatorProps={{ position: "bottom-start", offset: -6 }}
          labelContent={
            <Flex align="center" gap={rem(6)}>
              <IconCircleCheckFilled
                style={{ width: rem(18), height: rem(18) }}
                color="#3DA100"
              />
              <Text size={rem(14)} fw={500} c="#323232B2">
                Payment verified
              </Text>
            </Flex>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.4}
        factorY={0.9}
        className={classes.images}
        style={{
          top: "65.5%",
          left: "30%",
        }}
      >
        <ImageWithIndicator
          url="/brands/brands-4.png"
          imageProps={{ w: rem(90) }}
          isMobile={isMobile}
          indicatorProps={{ position: "bottom-start", offset: -6 }}
          labelContent={
            <Flex align="center" gap={rem(4)}>
              <IconStarFilled
                color="#FFBA09"
                style={{ width: rem(14), height: rem(14) }}
              />
              <Text fw={400} size="sm">
                4.2
              </Text>
            </Flex>
          }
        />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0}
        factorY={0}
        className={classes.images}
        style={{
          top: "0",
          left: "0",
        }}
      >
        <Image src="/hero-bg-top-left.png" w="50%" />
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0}
        factorY={0}
        className={classes.images}
        style={{
          bottom: "0",
          right: "0",
        }}
      >
        <Image src="/hero-bg-bottom-right.png" w={rem(60)} />
      </MouseParallaxChild>
    </MouseParallaxContainer>
  );
};

export default HeroImagesSection;
