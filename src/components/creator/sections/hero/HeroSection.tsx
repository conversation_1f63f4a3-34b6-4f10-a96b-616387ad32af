import Button from "@/components/common/button/Button";
import { COLECTA_APP_SIGNUP_URL } from "@/components/common/constants/urls";
import { Center, Container, rem, Stack, Text, Title } from "@mantine/core";
import { IconSearch } from "@tabler/icons-react";
import Link from "next/link";
import HeroImagesSection from "./HeroImagesSection";
import classes from "./HeroSection.module.css";

const HeroSectionMobile = () => {
  return (
    <Stack align="center" hiddenFrom="lg">
      <Container className={classes.heroSectionTitleContainer}>
        <Stack align="center" ta="center">
          <Title order={1} size={rem(26)} fw={400} w="90%" tt="capitalize">
            Turn Your Passion into Purposeful Partnerships
          </Title>
          <Center>
            <Text fw={300} size={rem(14)} lh={rem(20)} ta="center" maw="75%">
              With Colecta.ai, connect with brands that value your voice and
              empower you to make an impact.
            </Text>
          </Center>
          <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
            <Button
              mt="xl"
              fs="italic"
              size="lg"
              color="#C4EE34"
              fw={500}
              c="#000"
              rightSection={
                <IconSearch
                  style={{
                    width: rem(16),
                    height: rem(16),
                    marginLeft: rem(36),
                    transform: "rotate(90deg)",
                  }}
                />
              }
            >
              Explore Collaborations
            </Button>
          </Link>
        </Stack>
      </Container>
      <HeroImagesSection isMobile />
    </Stack>
  );
};

const HeroSectionDesktop = () => {
  return (
    <Stack align="center" visibleFrom="lg">
      <Container className={classes.heroSectionTitleContainer}>
        <Stack align="center" ta="center">
          <Title order={1} size={rem(46)} fw={400} tt="capitalize">
            Begin your journey to transform your creativity into meaningful
            collaborations
          </Title>
          <Center>
            <Text fw={300} size={rem(20)} lh={rem(28)} ta="center" maw="75%">
              Unlock your power with Colecta.ai – brands that listen to you,
              support, and empower you to create change! Your voice matters,
              make it heard with the intelligence of AI.
            </Text>
          </Center>
          <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
            <Button
              mt="xl"
              fs="italic"
              size="md"
              color="#C4EE34"
              fw={500}
              c="#000"
              rightSection={
                <IconSearch
                  style={{
                    width: rem(16),
                    height: rem(16),
                    marginLeft: rem(36),
                    transform: "rotate(90deg)",
                  }}
                />
              }
            >
              Explore Collaborations
            </Button>
          </Link>
        </Stack>
      </Container>
      <HeroImagesSection />
    </Stack>
  );
};

const HeroSection = () => {
  return (
    <>
      <HeroSectionDesktop />
      <HeroSectionMobile />
    </>
  );
};

export default HeroSection;
