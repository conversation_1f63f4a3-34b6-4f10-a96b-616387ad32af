import Button from "@/components/common/button/Button";
import { COLECTA_APP_SIGNUP_URL } from "@/components/common/constants/urls";
import { Box, Flex, Group, List, rem, Stack, Text, Title } from "@mantine/core";
import { IconArrowRight } from "@tabler/icons-react";
import Link from "next/link";
import classes from "./OldVsNewSection.module.css";

const OldVsNewTitleMobile = () => {
  return (
    <Flex justify="center" hiddenFrom="lg">
      <Stack align="center" gap={rem(8)} ta="center">
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          The New Way vs. The Old Way
        </Text>
        <Title order={1} size={rem(34)} fw={400} w="80%">
          Explore the Colecta's AI - way
        </Title>
        <Text
          fw={400}
          lh={rem(22)}
          size={rem(14)}
          c="#000000CC"
          ta="center"
          w="90%"
        >
          It is not about short-term gains, but more about building
          relationships that empower your journey and amplify your impact.
        </Text>
        <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
          <Button
            mt="xl"
            fs="italic"
            size="lg"
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
                stroke={1.5}
              />
            }
          >
            Explore "The AI -Way"
          </Button>
        </Link>
      </Stack>
    </Flex>
  );
};
const OldVsNewTitleDesktop = () => {
  return (
    <Flex justify="center" visibleFrom="lg">
      <Stack align="center" gap={rem(8)}>
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          INNOVATIVE SOLUTIONS V/S CONVENTIONAL METHODS
        </Text>
        <Title order={1} size={rem(36)} fw={400} w="45%" ta="center">
          Move Ahead of One-Offs – Create Genuine and Lasting Collaborations
        </Title>
        <Text
          fw={400}
          lh={rem(25)}
          size={rem(16)}
          c="#000000CC"
          ta="center"
          w="60%"
        >
          Say goodbye to one-off deals and get hold of meaningful partnerships
          that elevate your growth as a creator. It is not about short-term
          gains, but more about building relationships that empower your journey
          and amplify your impact.
        </Text>
        <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
          <Button
            mt="xl"
            fs="italic"
            size="md"
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
                stroke={1.5}
              />
            }
          >
            Explore "The AI -Way"
          </Button>
        </Link>
      </Stack>
    </Flex>
  );
};

const OldVsNewContentSectionMobile = () => {
  return (
    <Stack
      mx={rem(24)}
      mb={rem(24)}
      gap="0"
      hiddenFrom="lg"
      style={{ position: "relative", top: "-60px" }}
    >
      <Box p={rem(36)} className={classes.topContainer} h={rem(380)}>
        <Stack gap={rem(16)}>
          <Text
            size={rem(24)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Conventional Methods
          </Text>
          <List size="md">
            <List.Item c="#00000099" my="md">
              Random single-event collaboration
            </List.Item>
            <List.Item c="#00000099" my="md">
              Standardized and generic approach
            </List.Item>
            <List.Item c="#00000099" my="md">
              Non-transparent evaluation methods
            </List.Item>
            <List.Item c="#00000099" my="md">
              Sparse resources and inadequate aid
            </List.Item>
          </List>
        </Stack>
      </Box>

      <Box
        py={rem(24)}
        px={rem(36)}
        className={classes.bottomContainer}
        h={rem("100%")}
      >
        <Stack gap={rem(16)}>
          <Text
            size={rem(24)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Innovative Methods
          </Text>
          <List size="md">
            <List.Item c="#00000099" my="md">
              Mutually beneficial collaborations
            </List.Item>
            <List.Item c="#00000099" my="md">
              Customized and aligned opportunities
            </List.Item>
            <List.Item c="#00000099" my="md">
              Transparent objectives and milestones
            </List.Item>
            <List.Item c="#00000099" my="md">
              Complete guidance
            </List.Item>
          </List>
        </Stack>
      </Box>
    </Stack>
  );
};

const OldVsNewContentSectionDesktop = () => {
  return (
    <Group ml="15%" mr="3%" mt="5%" grow gap="0" visibleFrom="lg">
      <Box
        py={rem(52)}
        px={rem(60)}
        className={classes.leftContainer}
        maw="45%"
        h={rem(420)}
      >
        <Stack gap={rem(40)}>
          <Text
            size={rem(38)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Conventional Methods
          </Text>
          <List size="xl">
            <List.Item c="#00000099" my="md">
              Random single-event collaboration
            </List.Item>
            <List.Item c="#00000099" my="md">
              Standardized and generic approach
            </List.Item>
            <List.Item c="#00000099" my="md">
              Non-transparent evaluation methods
            </List.Item>
            <List.Item c="#00000099" my="md">
              Sparse resources and inadequate aid
            </List.Item>
          </List>
        </Stack>
      </Box>
      <Box
        className={classes.rightContainer}
        px={rem(72)}
        py={rem(52)}
        h={rem(420)}
      >
        <Stack gap={rem(40)}>
          <Text
            size={rem(38)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Innovative Methods
          </Text>
          <List size="xl" pl="xl">
            <List.Item c="#00000099" my="md">
              Mutually beneficial collaborations
            </List.Item>
            <List.Item c="#00000099" my="md">
              Customized and aligned opportunities
            </List.Item>
            <List.Item c="#00000099" my="md">
              Transparent objectives and milestones
            </List.Item>
            <List.Item c="#00000099" my="md">
              Complete guidance
            </List.Item>
          </List>
        </Stack>
      </Box>
    </Group>
  );
};

const OldVsNewSection = () => {
  return (
    <Stack mt="10%">
      <>
        <OldVsNewTitleDesktop />
        <OldVsNewTitleMobile />
        <OldVsNewContentSectionMobile />
        <OldVsNewContentSectionDesktop />
      </>
    </Stack>
  );
};

export default OldVsNewSection;
