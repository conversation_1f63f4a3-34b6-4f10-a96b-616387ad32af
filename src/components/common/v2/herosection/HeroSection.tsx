"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Group,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import { IconRobot, IconSparkles } from "@tabler/icons-react";
import { motion } from "framer-motion";
import { AIAgentDemo } from "../aiagentdemo/AIAgentDemo";
import classes from "./HeroSection.module.css";

interface HeroSectionProps {
  userType: "creator" | "brand";
}

export function HeroSection({ userType }: HeroSectionProps) {
  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };

  return (
    <>
      <Box component="section" hiddenFrom="sm" className={classes.heroSection}>
        <Container size="xl">
          <Stack align="center" gap="xl">
            {/* Hero Content */}
            <motion.div
              key={userType}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className={classes.heroContent}
            >
              <Title className={classes.heroTitleMobile}>
                {userType === "creator" ? (
                  <>
                    Your Personal{" "}
                    <span className={classes.gradientText}>
                      AI Collaboration
                    </span>{" "}
                    Manager
                  </>
                ) : (
                  <>
                    Discover{" "}
                    <span className={classes.gradientText}>
                      Perfect Creators
                    </span>{" "}
                    Instantly
                  </>
                )}
              </Title>

              <Text className={classes.heroText}>
                {userType === "creator"
                  ? "Transform your content creation journey with an AI agent that finds perfect brand matches, negotiates optimal deals, and provides strategic insights."
                  : "Leverage advanced AI to identify, analyze, and connect with the most relevant creators for your brand's unique marketing goals."}
              </Text>

              <Group justify="center" mt={40}>
                <Button
                  size="sm"
                  variant="gradient"
                  gradient={{ from: "purple", to: "blue", deg: 45 }}
                  leftSection={<IconSparkles size={16} />}
                  onClick={() => scrollToSection("early-access-section")}
                >
                  Get Early Access
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  leftSection={<IconRobot size={16} />}
                  onClick={() => scrollToSection("how-it-works-section")}
                >
                  How It Works
                </Button>
              </Group>
            </motion.div>

            {/* AI Agent Demo */}
            <motion.div
              key={`demo-${userType}`}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className={classes.demoContainer}
            >
              <AIAgentDemo userType={userType} />
            </motion.div>
          </Stack>
        </Container>
      </Box>

      <Box component="section" visibleFrom="sm" className={classes.heroSection}>
        <Container size="xl">
          <Stack align="center" gap="xl">
            {/* Hero Content */}
            <motion.div
              key={userType}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className={classes.heroContent}
            >
              <Title className={classes.heroTitle}>
                {userType === "creator" ? (
                  <>
                    Your Personal{" "}
                    <span className={classes.gradientText}>
                      AI Collaboration
                    </span>{" "}
                    Manager
                  </>
                ) : (
                  <>
                    Discover{" "}
                    <span className={classes.gradientText}>
                      Perfect Creators
                    </span>{" "}
                    Instantly
                  </>
                )}
              </Title>

              <Text className={classes.heroText}>
                {userType === "creator"
                  ? "Transform your content creation journey with an AI agent that finds perfect brand matches, negotiates optimal deals, and provides strategic insights."
                  : "Leverage advanced AI to identify, analyze, and connect with the most relevant creators for your brand's unique marketing goals."}
              </Text>

              <Group justify="center" mt={40}>
                <Button
                  size="sm"
                  variant="gradient"
                  gradient={{ from: "purple", to: "blue", deg: 45 }}
                  leftSection={<IconSparkles size={16} />}
                  onClick={() => scrollToSection("early-access-section")}
                >
                  Get Early Access
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  leftSection={<IconRobot size={16} />}
                  onClick={() => scrollToSection("how-it-works-section")}
                >
                  How It Works
                </Button>
              </Group>
            </motion.div>

            {/* AI Agent Demo */}
            <motion.div
              key={`demo-${userType}`}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className={classes.demoContainer}
            >
              <AIAgentDemo userType={userType} />
            </motion.div>
          </Stack>
        </Container>
      </Box>
    </>
  );
}
