/* HeroSection.module.css */
.heroSection {
  position: relative;
  background-color: var(--mantine-color-dark-7);
  overflow: hidden;
  padding: 80px 0;
}

.heroSection::before {
  content: "";
  position: absolute;
  top: -200px;
  right: -200px;
  width: 500px;
  height: 500px;
  background: radial-gradient(circle,
      rgba(138, 111, 223, 0.2) 0%,
      rgba(138, 111, 223, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.heroSection::after {
  content: "";
  position: absolute;
  bottom: -200px;
  left: -200px;
  width: 500px;
  height: 500px;
  background: radial-gradient(circle,
      rgba(100, 217, 208, 0.2) 0%,
      rgba(100, 217, 208, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.heroContent {
  text-align: center;
  position: relative;
  z-index: 1;
}

.heroTitle {
  font-size: 7rem;
  line-height: 1.2;
  font-weight: 800;
  color: var(--mantine-color-white);
  margin-bottom: 20px;
}

.heroTitleMobile {
  font-size: 3.8rem;
  line-height: 1.2;
  font-weight: 800;
  color: var(--mantine-color-white);
  margin-bottom: 50px;
}

.gradientText {
  background: linear-gradient(135deg, #8a6fdf 0%, #64d9d0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.heroText {
  color: var(--mantine-color-gray-3);
  margin-bottom: 20px;
  font-size: 1rem;
  margin-left: auto;
  margin-right: auto;
}

.demoContainer {
  max-width: 90vw;
  width: 100%;
  position: relative;
  z-index: 1;
}