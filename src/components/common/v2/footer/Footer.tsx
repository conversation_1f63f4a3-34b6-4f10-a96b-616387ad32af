"use client";

import {
  ActionIcon,
  Box,
  Container,
  Divider,
  Flex,
  Group,
  Image,
  rem,
  Text,
} from "@mantine/core";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import classes from "./Footer.module.css";

const links = [
  { link: "/", label: "Home" },
  { link: "/forcreators", label: "For Creators" },
  { link: "/forbrands", label: "For Brands" },
  { link: "/privacy-policy", label: "Privacy" },
  { link: "/terms-and-conditions", label: "Terms" },
  { link: "/refund-policy", label: "Refund Policy" },
];

export function Footer() {
  const router = useRouter();
  const items = links.map((link) => (
    <Link key={link.label} href={link.link} className={classes.link}>
      {link.label}
    </Link>
  ));

  const navigateToHome = () => {
    router.push("/");
  };

  return (
    <Box component="footer" className={classes.footer}>
      <Container size="xl" mt="lg">
        <Flex
          direction={{ base: "column", sm: "row" }}
          justify="space-between"
          align={{ base: "center", sm: "flex-start" }}
          gap={{ base: "md", sm: "xl" }}
          py="md"
        >
          <Flex direction="column" align={{ base: "center", sm: "flex-start" }}>
            <Image
              src="./colecta-logo.svg"
              w={rem(100)}
              h={{ base: rem(32), md: rem(36) }}
              mb="xs"
              onClick={navigateToHome}
              style={{ cursor: "pointer" }}
            />
            <Text size="sm" c="dimmed" mt={5} mb="md">
              Personalized AI Agents for Creators & Brands
            </Text>
          </Flex>

          <Flex
            direction={{ base: "column", sm: "row" }}
            gap={{ base: "sm", sm: "lg" }}
            align="center"
            wrap="wrap"
          >
            {items}
          </Flex>

          <Group gap="md" justify="center">
            <ActionIcon
              component="a"
              href="https://x.com/colecta_ai"
              size="lg"
              variant="subtle"
              radius="xl"
              target="_blank"
            >
              <FaXTwitter size="1.2rem" />
            </ActionIcon>
            <ActionIcon
              component="a"
              href="https://www.instagram.com/colecta.ai/"
              size="lg"
              variant="subtle"
              radius="xl"
              target="_blank"
            >
              <FaInstagram size="1.2rem" />
            </ActionIcon>
            <ActionIcon
              component="a"
              href="https://www.linkedin.com/company/colecta-ai/"
              size="lg"
              variant="subtle"
              radius="xl"
              target="_blank"
            >
              <FaLinkedin size="1.2rem" />
            </ActionIcon>
            <ActionIcon
              component="a"
              href="https://www.facebook.com/profile.php?id=61555986834688"
              size="lg"
              variant="subtle"
              radius="xl"
              target="_blank"
            >
              <FaFacebook size="1.2rem" />
            </ActionIcon>
          </Group>
        </Flex>

        <Divider my="md" />

        <Text size="sm" c="dimmed" ta="center" py="md">
          © {new Date().getFullYear()} Colecta AI. All rights reserved.
        </Text>
      </Container>
    </Box>
  );
}
