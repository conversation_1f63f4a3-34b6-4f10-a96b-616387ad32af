/* src/components/common/SignupForm.module.css */
.paper {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.input {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  
  &:focus {
    border-color: var(--mantine-color-purple-5);
    background: rgba(255, 255, 255, 0.05);
  }
}

.inputLabel {
  color: var(--mantine-color-gray-5);
  font-weight: 500;
  margin-bottom: 4px;
}

.googleButton {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
  }
}

.divider {
  flex: 1;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.link {
  color: var(--mantine-color-purple-4);
  text-decoration: none;
  transition: color 0.2s ease;
  
  &:hover {
    color: var(--mantine-color-purple-5);
    text-decoration: underline;
  }
}

.gradientText {
  background: linear-gradient(45deg, var(--mantine-color-purple-4), var(--mantine-color-purple-6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.statsContainer {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--mantine-radius-md);
  padding: var(--mantine-spacing-lg);
  width: 100%;
  margin-top: var(--mantine-spacing-xl);
}

@media (max-width: $mantine-breakpoint-sm) {
  .statsContainer {
    padding: var(--mantine-spacing-md);
  }
}
