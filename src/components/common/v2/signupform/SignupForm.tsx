"use client";

import {
  Box,
  Group,
  rem,
  Stack,
  Text,
  TextInput,
  Tooltip,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { IconInfoCircle } from "@tabler/icons-react";
import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";
import { FaGoogle } from "react-icons/fa";
import { Button } from "../button/Button";
import classes from "./SignupForm.module.css";

interface SignupFormProps {
  userType: "creator" | "brand";
}

export function SignupForm({ userType }: SignupFormProps) {
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm({
    initialValues: {
      email: "",
      name: "",
      website: userType === "brand" ? "" : undefined,
      instagram: userType === "creator" ? "" : undefined,
      companySize: userType === "brand" ? "" : undefined,
    },
    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Invalid email"),
      name: (value) => (value.trim().length > 0 ? null : "Name is required"),
    },
  });

  const handleSubmit = async (values: typeof form.values) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));
    console.log("Form submitted:", values);
    setIsLoading(false);
    setSubmitted(true);
  };

  if (submitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Stack align="center" gap="xl" py="xl">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 10, 0, -10, 0],
            }}
            transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 2 }}
          >
            <Text size={rem(64)}>🎉</Text>
          </motion.div>

          <Stack gap="md" align="center">
            <Text size="xl" fw={700} className={classes.gradientText}>
              Welcome to the Future!
            </Text>
            <Text c="dimmed" ta="center" maw={400}>
              You're now on the exclusive early access list for Colecta AI.
              We'll be in touch shortly with your next steps.
            </Text>
          </Stack>

          <Box className={classes.statsContainer}>
            <Group gap={48} justify="center">
              <Stack gap={0} align="center">
                <Text size="xl" fw={700}>
                  200+
                </Text>
                <Text size="sm" c="dimmed">
                  Brands Onboard
                </Text>
              </Stack>
              <Stack gap={0} align="center">
                <Text size="xl" fw={700}>
                  48h
                </Text>
                <Text size="sm" c="dimmed">
                  Avg. Response
                </Text>
              </Stack>
            </Group>
          </Box>
        </Stack>
      </motion.div>
    );
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <Button
              leftSection={<FaGoogle size={16} />}
              variant="outline"
              color="gray"
              fullWidth
              size="md"
              className={classes.googleButton}
            >
              Continue with Google
            </Button>

            <Group gap={8} justify="center">
              <Box className={classes.divider} />
              <Text size="sm" c="dimmed">
                or
              </Text>
              <Box className={classes.divider} />
            </Group>

            <TextInput
              label="Full Name"
              placeholder="John Doe"
              required
              classNames={{
                input: classes.input,
                label: classes.inputLabel,
              }}
              {...form.getInputProps("name")}
            />

            <TextInput
              label="Work Email"
              placeholder="<EMAIL>"
              required
              classNames={{
                input: classes.input,
                label: classes.inputLabel,
              }}
              {...form.getInputProps("email")}
            />

            {userType === "brand" && (
              <>
                <TextInput
                  label={
                    <Group gap={6}>
                      <Text>Company Website</Text>
                      <Tooltip label="Help us verify your business">
                        <IconInfoCircle size={16} style={{ opacity: 0.5 }} />
                      </Tooltip>
                    </Group>
                  }
                  placeholder="www.company.com"
                  classNames={{
                    input: classes.input,
                    label: classes.inputLabel,
                  }}
                  {...form.getInputProps("website")}
                />

                <TextInput
                  label="Company Size"
                  placeholder="e.g., 10-50 employees"
                  classNames={{
                    input: classes.input,
                    label: classes.inputLabel,
                  }}
                  {...form.getInputProps("companySize")}
                />
              </>
            )}

            {userType === "creator" && (
              <TextInput
                label="Instagram Handle"
                placeholder="@yourhandle"
                classNames={{
                  input: classes.input,
                  label: classes.inputLabel,
                }}
                {...form.getInputProps("instagram")}
              />
            )}

            <Button
              variant="gradient"
              gradientType="primary"
              size="md"
              fullWidth
              loading={isLoading}
              loaderProps={{ size: "sm" }}
            >
              {isLoading ? "Processing..." : "Join Early Access"}
            </Button>

            <Text size="xs" ta="center" c="dimmed">
              By signing up, you agree to our{" "}
              <Text component="a" href="#" className={classes.link}>
                Terms of Service
              </Text>{" "}
              and{" "}
              <Text component="a" href="#" className={classes.link}>
                Privacy Policy
              </Text>
            </Text>
          </Stack>
        </form>
      </motion.div>
    </AnimatePresence>
  );
}
