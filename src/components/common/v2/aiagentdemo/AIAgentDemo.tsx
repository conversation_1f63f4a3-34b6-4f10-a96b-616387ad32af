import {
  ActionIcon,
  Avatar,
  Badge,
  Box,
  Container,
  Flex,
  Grid,
  Group,
  Paper,
  Progress,
  Stack,
  Text,
  rem,
} from "@mantine/core";
import {
  IconBell,
  IconBulb,
  IconChartBar,
  IconChartLine,
  IconHeartHandshake,
  IconMessage,
  IconRobot,
  IconSend,
  IconSettings,
  IconSparkles,
  IconTarget,
  IconTrophy,
  IconUsers,
  IconX,
} from "@tabler/icons-react";
import { AnimatePresence, motion } from "framer-motion";
import React, { useEffect, useState } from "react";
import styles from "./AIAgentDemo.module.css";

interface AIAgentDemoProps {
  userType: "creator" | "brand";
}

interface AIAction {
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  timestamp: string;
  progress?: number;
}

interface Metric {
  label: string;
  value: string;
  change: number;
  icon: React.ReactNode;
}

interface Notification {
  id: string;
  message: string;
  subtext: string;
  type: "success" | "info";
  icon: React.ReactNode;
}

// First, create a type for the notification template
type NotificationTemplate = {
  message: string;
  subtext: string;
  icon: React.ReactNode;
  type: "success" | "info";
};

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.5, ease: "easeOut" },
  },
};

const pulseAnimation = {
  "@keyframes pulse": {
    "0%": { opacity: 1, transform: "scale(1)" },
    "50%": { opacity: 0.5, transform: "scale(1.1)" },
    "100%": { opacity: 1, transform: "scale(1)" },
  },
};

const glowStyles = {
  position: "relative" as const,
  "&::before": {
    content: '""',
    position: "absolute" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: "inherit",
    background:
      "linear-gradient(45deg, rgba(56, 189, 248, 0.1), rgba(168, 85, 247, 0.1))",
    filter: "blur(20px)",
    opacity: 0.5,
    zIndex: -1,
  },
  "&::after": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: "inherit",
    animation: `${pulseAnimation["@keyframes pulse"]} 2s infinite ease-in-out`,
    background:
      "radial-gradient(circle at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%)",
    zIndex: -1,
  },
};

const containerGlowStyles = {
  position: "relative" as const,
  "&::before": {
    content: '""',
    position: "absolute",
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    background:
      "linear-gradient(45deg, rgba(99, 102, 241, 0.15), rgba(168, 85, 247, 0.15))",
    borderRadius: "inherit",
    filter: "blur(20px)",
    opacity: 0.5,
    zIndex: -1,
  },
};

const creatorNotifications: NotificationTemplate[] = [
  {
    message: "New collaboration request from Eco Essentials",
    subtext: "92% match with your profile",
    icon: <IconHeartHandshake size={16} />,
    type: "success",
  },
  {
    message: "Content performance update",
    subtext: "Your recent post exceeded engagement targets by 45%",
    icon: <IconChartBar size={16} />,
    type: "info",
  },
  {
    message: "AI Match Alert",
    subtext: "New brand match found: Green Living Co (89% compatibility)",
    icon: <IconBulb size={16} />,
    type: "success",
  },
  {
    message: "Campaign milestone achieved",
    subtext: "Successfully completed collaboration with Nature Foods",
    icon: <IconTrophy size={16} />,
    type: "success",
  },
];

const brandNotifications: NotificationTemplate[] = [
  {
    message: "Creator accepted your collaboration",
    subtext: "Emma Richardson (95% match) accepted your proposal",
    icon: <IconHeartHandshake size={16} />,
    type: "success",
  },
  {
    message: "Campaign performance update",
    subtext: "Current ROI tracking 25% above target",
    icon: <IconChartBar size={16} />,
    type: "info",
  },
  {
    message: "AI Creator Match",
    subtext: "New creator found: Sarah Miller (91% compatibility)",
    icon: <IconBulb size={16} />,
    type: "success",
  },
  {
    message: "Content milestone reached",
    subtext: "Campaign content receiving 2x expected engagement",
    icon: <IconTrophy size={16} />,
    type: "success",
  },
];

export function AIAgentDemo({ userType }: AIAgentDemoProps) {
  const [aiActions, setAIActions] = useState<AIAction[]>([]);
  const [notification, setNotification] = useState<Notification | null>(null);

  const showNotification = () => {
    const notifications =
      userType === "creator" ? creatorNotifications : brandNotifications;
    const randomNotification =
      notifications[Math.floor(Math.random() * notifications.length)];

    const newNotification: Notification = {
      id: Date.now().toString(),
      message: randomNotification.message,
      subtext: randomNotification.subtext,
      type: randomNotification.type,
      icon: randomNotification.icon,
    };

    setNotification(newNotification);

    setTimeout(() => setNotification(null), 5000); // 5 seconds
  };

  const metrics: Metric[] =
    userType === "creator"
      ? [
          {
            label: "Active Collaborations",
            value: "12",
            change: 8.1,
            icon: <IconUsers style={{ color: "#a855f7" }} />,
          },
          {
            label: "Brand Matches",
            value: "89%",
            change: 12.5,
            icon: <IconTarget style={{ color: "#38bdf8" }} />,
          },
          {
            label: "AI Suggestions",
            value: "24",
            change: 15.3,
            icon: <IconBulb style={{ color: "#0ea5e9" }} />,
          },
          {
            label: "Campaign Success",
            value: "92%",
            change: 5.7,
            icon: <IconChartLine style={{ color: "#22c55e" }} />,
          },
        ]
      : [
          {
            label: "Active Creators",
            value: "28",
            change: 10.2,
            icon: <IconUsers style={{ color: "#a855f7" }} />,
          },
          {
            label: "Campaign ROI",
            value: "215%",
            change: 18.4,
            icon: <IconChartLine style={{ color: "#38bdf8" }} />,
          },
          {
            label: "Creator Matches",
            value: "94%",
            change: 7.8,
            icon: <IconTarget style={{ color: "#0ea5e9" }} />,
          },
          {
            label: "Active Campaigns",
            value: "8",
            change: 12.3,
            icon: <IconSparkles style={{ color: "#22c55e" }} />,
          },
        ];

  const generateCreatorAIActions = (): AIAction[] => [
    {
      id: "pitch-sent",
      icon: <IconSend style={{ width: rem(24), height: rem(24) }} />,
      title: "Collaboration Pitch Sent",
      description: "Proposed collaboration with Eco Essentials - 92% match",
      timestamp: "Just now",
      progress: 92,
    },
    {
      id: "content-optimized",
      icon: <IconBulb style={{ width: rem(24), height: rem(24) }} />,
      title: "Content Strategy Optimized",
      description: "Suggested sustainable living content themes",
      timestamp: "2 mins ago",
    },
    {
      id: "brand-matched",
      icon: <IconTarget style={{ width: rem(24), height: rem(24) }} />,
      title: "Brand Match Found",
      description: "Identified potential collaboration with Green Living Co.",
      timestamp: "5 mins ago",
      progress: 85,
    },
  ];

  const generateBrandAIActions = (): AIAction[] => [
    {
      id: "creator-matched",
      icon: <IconSparkles style={{ width: rem(24), height: rem(24) }} />,
      title: "Top Creator Identified",
      description: "Emma Richardson - 95% match for Eco campaign",
      timestamp: "Just now",
      progress: 95,
    },
    {
      id: "campaign-optimized",
      icon: <IconChartLine style={{ width: rem(24), height: rem(24) }} />,
      title: "Campaign Strategy Refined",
      description: "Adjusted targeting for maximum reach",
      timestamp: "3 mins ago",
    },
    {
      id: "outreach-initiated",
      icon: <IconMessage style={{ width: rem(24), height: rem(24) }} />,
      title: "Creator Outreach Initiated",
      description: "Personalized pitch drafted for top creators",
      timestamp: "7 mins ago",
    },
  ];

  useEffect(() => {
    const initialActions =
      userType === "creator"
        ? generateCreatorAIActions()
        : generateBrandAIActions();
    setAIActions(initialActions);

    const interval = setInterval(() => {
      const newAction =
        userType === "creator"
          ? generateCreatorAIActions()[0] // Get only the first action
          : generateBrandAIActions()[0]; // Get only the first action

      setAIActions((prev) => {
        // Keep only the last 5 actions
        const updatedActions = [newAction, ...prev];
        return updatedActions.slice(0, 5);
      });
    }, 30000);

    const notificationInterval = setInterval(() => {
      showNotification();
    }, 15000); // Show a new notification every 15 seconds

    return () => {
      clearInterval(interval);
      clearInterval(notificationInterval);
    };
  }, [userType]);

  return (
    <div
      className="relative"
      style={{
        transform: "scale(1.1)", // Reduced from 1.2 to 1.1
        transformOrigin: "center top",
        marginBottom: "15%", // Reduced margin since scale is smaller
      }}
    >
      {/* MOBILE VIEW FOR AI DEMO */}
      <Container
        size="100%"
        py="xs"
        px="xs"
        hiddenFrom="sm"
        style={{
          maxWidth: "1400px",
        }}
      >
        <Box
          style={{
            // height: "66vh", // Increased from 60vh to 66vh (10% increase)
            overflow: "hidden",
            borderRadius: "var(--mantine-radius-md)",
            border: "1px solid rgba(99, 102, 241, 0.2)",
            background:
              "linear-gradient(145deg, rgba(30, 30, 35, 0.95), rgba(20, 20, 24, 0.95))",
            backdropFilter: "blur(12px)",
            boxShadow:
              "0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(99, 102, 241, 0.1)",
            display: "flex",
            flexDirection: "column",
            width: "100%",
          }}
        >
          {/* Top Navigation Bar */}
          <Group
            justify="space-between"
            px="md"
            py="xs"
            wrap="nowrap"
            style={{
              flexShrink: 0,
              borderBottom: "1px solid rgba(255, 255, 255, 0.05)",
              minHeight: "40px", // Reduced height
            }}
          >
            <Group wrap="nowrap">
              <Text fw={700} size="xs" c="white">
                {userType === "creator"
                  ? "Creator Studio"
                  : "Brand Command Center"}
              </Text>
              <Group gap={8}>
                <Badge
                  variant="dot"
                  size="sm"
                  styles={{
                    root: {
                      textTransform: "none",
                      padding: "4px 8px",
                    },
                  }}
                >
                  {userType === "creator" ? "Pro" : "Enterprise"}
                </Badge>

                {/* Add AI Status Indicator */}
                <Group gap={4} style={{ alignItems: "center" }}>
                  <div className={styles.statusDot} />
                  <Text size="xs" c="dimmed" style={{ letterSpacing: "0.3px" }}>
                    AI Agent Active
                  </Text>
                </Group>
              </Group>
            </Group>
            <Group gap={8} wrap="nowrap">
              <ActionIcon variant="subtle" color="gray" size="sm">
                <IconBell size={16} />
              </ActionIcon>
              <ActionIcon variant="subtle" color="gray" size="sm">
                <IconSettings size={16} />
              </ActionIcon>
              <Avatar
                radius="xl"
                size="sm"
                src={
                  userType === "creator"
                    ? "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80"
                    : "https://images.unsplash.com/photo-1560250097-0b93528c311a?auto=format&fit=crop&q=80"
                }
              />
            </Group>
          </Group>

          {/* Main Content Area */}
          <Box
            p="xs" // Reduced from sm to xs
            style={{
              flex: 1,
              overflow: "hidden",
              display: "flex",
              flexDirection: "column",
              gap: "8px", // Reduced spacing between elements
            }}
          >
            {/* Notifications */}
            <AnimatePresence>
              {notification && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  style={{
                    position: "absolute",
                    top: "70px",
                    left: "5%",
                    transform: "translateX(-50%)",
                    zIndex: 50,
                    width: "400px",
                    maxWidth: "90%",
                  }}
                >
                  <Paper
                    p="md"
                    radius="md"
                    style={{
                      background:
                        "linear-gradient(145deg, rgba(30, 30, 35, 0.98), rgba(20, 20, 24, 0.98))",
                      backdropFilter: "blur(12px)",
                      border: "1px solid rgba(99, 102, 241, 0.2)",
                      boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
                      ...glowStyles,
                    }}
                  >
                    <Group gap="sm" align="flex-start">
                      {/* Add AI Agent Avatar/Icon */}
                      <Avatar
                        size="sm"
                        radius="xl"
                        style={{
                          background:
                            "linear-gradient(45deg, #4f46e5, #8b5cf6)",
                          border: "2px solid rgba(255, 255, 255, 0.2)",
                          boxShadow: "0 0 20px rgba(99, 102, 241, 0.3)",
                        }}
                      >
                        <IconRobot size={14} style={{ color: "white" }} />
                      </Avatar>

                      <Stack gap={2} style={{ flex: 1 }}>
                        <Text
                          size="xs"
                          c="dimmed"
                          style={{ letterSpacing: "0.3px", color: "#a5b4fc" }}
                        >
                          AI Agent Update
                        </Text>
                        <Text fw={500} size="sm" c="white">
                          {notification.message}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {notification.subtext}
                        </Text>
                      </Stack>

                      <ActionIcon
                        variant="subtle"
                        color="gray"
                        size="sm"
                        onClick={() => setNotification(null)}
                        style={{ opacity: 0.7 }}
                      >
                        <IconX size={14} />
                      </ActionIcon>
                    </Group>
                  </Paper>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Metrics Grid */}
            <Grid mb="sm" style={{ flexShrink: 0 }}>
              {metrics.map((metric, index) => (
                <Grid.Col key={index} span={{ base: 6, sm: 6, md: 3 }}>
                  <Paper
                    p="sm"
                    radius="md"
                    style={{
                      background: "rgba(30, 30, 35, 0.5)",
                      border: "1px solid rgba(99, 102, 241, 0.1)",
                      boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
                      transition: "transform 0.2s ease, box-shadow 0.2s ease",
                      "&:hover": {
                        transform: "translateY(-2px)",
                        boxShadow: "0 6px 24px rgba(0, 0, 0, 0.3)",
                      },
                    }}
                  >
                    <Group justify="space-between" mb="xs" wrap="nowrap">
                      <Text
                        size="xs"
                        c="dimmed"
                        style={{ letterSpacing: "0.3px" }}
                      >
                        {metric.label}
                      </Text>
                      <Box
                        style={{
                          background: "rgba(99, 102, 241, 0.1)",
                          padding: "4px",
                          borderRadius: "6px",
                        }}
                      >
                        {React.cloneElement(metric.icon as React.ReactElement, {
                          size: 14,
                        })}
                      </Box>
                    </Group>
                    <Group justify="space-between" align="flex-end">
                      <Text fw={500} size="sm" c="white">
                        {metric.value}
                      </Text>
                      <Badge
                        variant="light"
                        size="xs"
                        color={metric.change > 0 ? "indigo" : "red"}
                        styles={{
                          root: {
                            textTransform: "none",
                          },
                        }}
                      >
                        {metric.change > 0 ? "+" : ""}
                        {metric.change}%
                      </Badge>
                    </Group>
                  </Paper>
                </Grid.Col>
              ))}
            </Grid>

            {/* Activity Feeds */}
            <Stack gap="md" style={{ flex: 1, minHeight: 0 }}>
              {/* AI Activity Feed */}
              <Box style={{ flex: 2, overflow: "hidden" }}>
                <Paper
                  radius="md"
                  style={{
                    background: "rgba(30, 30, 35, 0.5)",
                    border: "1px solid rgba(99, 102, 241, 0.1)",
                    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <Box
                    px="md"
                    py="xs"
                    style={{
                      borderBottom: "1px solid rgba(255, 255, 255, 0.03)",
                      flexShrink: 0,
                    }}
                  >
                    <Group>
                      <Avatar
                        radius="xl"
                        size="sm"
                        style={{
                          background:
                            "linear-gradient(45deg, #6366f1, #8b5cf6)",
                        }}
                      >
                        <IconRobot size={14} style={{ color: "white" }} />
                      </Avatar>
                      <div>
                        <Text fw={500} size="xs" c="white">
                          AI Assistant
                        </Text>
                        <Text size="xs" c="dimmed">
                          Real-time Activity
                        </Text>
                      </div>
                    </Group>
                  </Box>
                  <Box
                    p="xs"
                    style={{ flex: 1, overflowY: "scroll" }}
                    mah={rem(350)}
                  >
                    <AnimatePresence>
                      {aiActions
                        .slice(aiActions.length - 4, aiActions.length)
                        .map((action) => (
                          <motion.div
                            key={`${action.id}-${Math.random()}`}
                            variants={itemVariants}
                            initial="hidden"
                            animate="visible"
                            exit={{ opacity: 0, y: 20 }}
                          >
                            <Paper
                              p="xs"
                              radius="md"
                              mb="xs"
                              style={{
                                background: "rgba(30, 30, 35, 0.4)",
                                border: "1px solid rgba(255, 255, 255, 0.05)",
                                transition: "transform 0.2s ease",
                                "&:hover": {
                                  transform: "translateX(4px)",
                                },
                              }}
                            >
                              <Group
                                justify="space-between"
                                mb="xs"
                                wrap="nowrap"
                              >
                                <Group
                                  gap="sm"
                                  justify="space-between"
                                  wrap="nowrap"
                                >
                                  <Box
                                    style={{
                                      background: "rgba(99, 102, 241, 0.1)",
                                      padding: "6px",
                                      borderRadius: "8px",
                                      color: "#6366f1",
                                    }}
                                  >
                                    {React.cloneElement(
                                      action.icon as React.ReactElement
                                    )}
                                  </Box>
                                  <Text fw={500} size="sm" c="white">
                                    {action.title}
                                  </Text>
                                  <Text
                                    size="xs"
                                    c="dimmed"
                                    style={{
                                      textAlign: "right",
                                    }}
                                  >
                                    {action.timestamp}
                                  </Text>
                                </Group>
                              </Group>
                              <div>
                                <Text size={rem(10)} c="dimmed">
                                  {action.description}
                                </Text>
                              </div>
                              {action.progress && (
                                <Progress
                                  value={action.progress}
                                  color="indigo"
                                  size="xs"
                                  radius="xl"
                                  mt="xs"
                                  style={{
                                    background: "rgba(255, 255, 255, 0.05)",
                                  }}
                                />
                              )}
                            </Paper>
                          </motion.div>
                        ))}
                    </AnimatePresence>
                  </Box>
                </Paper>
              </Box>

              {/* Active Tasks */}
              <Box style={{ flex: 1, overflow: "hidden" }}>
                <Paper
                  radius="md"
                  style={{
                    background: "rgba(30, 30, 35, 0.5)",
                    border: "1px solid rgba(99, 102, 241, 0.1)",
                    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <Box
                    p="sm"
                    style={{
                      borderBottom: "1px solid rgba(255, 255, 255, 0.05)",
                    }}
                  >
                    <Group justify="space-between">
                      <Text fw={500} size="sm" c="white">
                        Active Tasks
                      </Text>
                      <Badge variant="dot" color="indigo" size="xs">
                        3 In Progress
                      </Badge>
                    </Group>
                  </Box>

                  <Box
                    p="xs"
                    style={{
                      flex: 1,
                      overflowY: "auto",
                    }}
                  >
                    <Stack gap="xs">
                      {[
                        {
                          title:
                            userType === "creator"
                              ? "Brand Outreach"
                              : "Campaign Analysis",
                          progress: 75,
                          icon:
                            userType === "creator" ? (
                              <IconUsers size={16} />
                            ) : (
                              <IconChartLine size={16} />
                            ),
                          dueDate: "Today, 5:00 PM",
                        },
                        {
                          title:
                            userType === "creator"
                              ? "Content Planning"
                              : "Creator Matching",
                          progress: 45,
                          icon:
                            userType === "creator" ? (
                              <IconBulb size={16} />
                            ) : (
                              <IconTarget size={16} />
                            ),
                          dueDate: "Tomorrow, 10:00 AM",
                        },
                        {
                          title: "Performance Analysis",
                          progress: 90,
                          icon: <IconChartLine size={16} />,
                          dueDate: "Today, 3:00 PM",
                        },
                      ].map((task, index) => (
                        <Paper
                          key={index}
                          p="xs"
                          radius="md"
                          style={{
                            background: "rgba(30, 30, 35, 0.4)",
                            border: "1px solid rgba(255, 255, 255, 0.05)",
                            transition: "all 0.2s ease",
                            "&:hover": {
                              background: "rgba(35, 35, 40, 0.4)",
                              transform: "translateX(4px)",
                            },
                          }}
                        >
                          <Group justify="space-between" mb="xs">
                            <Group gap="sm">
                              <Box
                                style={{
                                  background: "rgba(99, 102, 241, 0.1)",
                                  padding: "6px",
                                  borderRadius: "8px",
                                  color: "#6366f1",
                                }}
                              >
                                {task.icon}
                              </Box>
                              <div>
                                <Text size="sm" fw={500} c="white">
                                  {task.title}
                                </Text>
                                <Text size="xs" c="dimmed">
                                  {task.dueDate}
                                </Text>
                              </div>
                            </Group>
                          </Group>
                          <Group justify="space-between">
                            <Progress
                              value={task.progress}
                              color="indigo"
                              size="xs"
                              radius="xl"
                              style={{
                                width: "80%",
                                background: "rgba(255, 255, 255, 0.05)",
                              }}
                            />
                            <Text size="xs" c="dimmed">
                              {task.progress}%
                            </Text>
                          </Group>
                        </Paper>
                      ))}
                    </Stack>
                  </Box>
                </Paper>
              </Box>
            </Stack>
          </Box>
        </Box>
      </Container>

      {/* DESKTOP VIEW FOR AI DEMO */}
      <Container
        size="100%"
        py="xs"
        px="xs"
        visibleFrom="sm"
        style={{
          maxWidth: "1400px",
          ...containerGlowStyles,
        }}
      >
        <Box
          style={{
            height: "66vh", // Increased from 60vh to 66vh (10% increase)
            overflow: "hidden",
            borderRadius: "var(--mantine-radius-md)",
            border: "1px solid rgba(99, 102, 241, 0.2)",
            background:
              "linear-gradient(145deg, rgba(30, 30, 35, 0.95), rgba(20, 20, 24, 0.95))",
            backdropFilter: "blur(12px)",
            boxShadow:
              "0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(99, 102, 241, 0.1)",
            display: "flex",
            flexDirection: "column",
            width: "100%",
          }}
        >
          {/* Top Navigation Bar */}
          <Group
            justify="space-between"
            px="md"
            py="xs"
            style={{
              flexShrink: 0,
              borderBottom: "1px solid rgba(255, 255, 255, 0.05)",
              minHeight: "40px", // Reduced height
            }}
          >
            <Group>
              <Text fw={500} size="sm" c="white">
                {userType === "creator"
                  ? "Creator Studio"
                  : "Brand Command Center"}
              </Text>
              <Group gap={8}>
                <Badge
                  variant="dot"
                  size="sm"
                  styles={{
                    root: {
                      textTransform: "none",
                      padding: "4px 8px",
                    },
                  }}
                >
                  {userType === "creator" ? "Pro" : "Enterprise"}
                </Badge>

                {/* Add AI Status Indicator */}
                <Group gap={4} style={{ alignItems: "center" }}>
                  <div className={styles.statusDot} />
                  <Text size="xs" c="dimmed" style={{ letterSpacing: "0.3px" }}>
                    AI Agent Active
                  </Text>
                </Group>
              </Group>
            </Group>
            <Group gap="xs">
              <ActionIcon variant="subtle" color="gray" size="sm">
                <IconBell size={16} />
              </ActionIcon>
              <ActionIcon variant="subtle" color="gray" size="sm">
                <IconSettings size={16} />
              </ActionIcon>
              <Avatar
                radius="xl"
                size="sm"
                src={
                  userType === "creator"
                    ? "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80"
                    : "https://images.unsplash.com/photo-1560250097-0b93528c311a?auto=format&fit=crop&q=80"
                }
              />
            </Group>
          </Group>

          {/* Main Content Area */}
          <Box
            p="xs" // Reduced from sm to xs
            style={{
              flex: 1,
              overflow: "hidden",
              display: "flex",
              flexDirection: "column",
              gap: "8px", // Reduced spacing between elements
            }}
          >
            {/* Notifications */}
            <AnimatePresence>
              {notification && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  style={{
                    position: "absolute",
                    top: "24px",
                    left: "50%",
                    transform: "translateX(-50%)",
                    zIndex: 50,
                    width: "400px",
                    maxWidth: "90%",
                  }}
                >
                  <Paper
                    p="md"
                    radius="md"
                    style={{
                      background:
                        "linear-gradient(145deg, rgba(30, 30, 35, 0.98), rgba(20, 20, 24, 0.98))",
                      backdropFilter: "blur(12px)",
                      border: "1px solid rgba(99, 102, 241, 0.2)",
                      boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
                      ...glowStyles,
                    }}
                  >
                    <Group gap="sm" align="flex-start">
                      {/* Add AI Agent Avatar/Icon */}
                      <Avatar
                        size="sm"
                        radius="xl"
                        style={{
                          background:
                            "linear-gradient(45deg, #4f46e5, #8b5cf6)",
                          border: "2px solid rgba(255, 255, 255, 0.2)",
                          boxShadow: "0 0 20px rgba(99, 102, 241, 0.3)",
                        }}
                      >
                        <IconRobot size={14} style={{ color: "white" }} />
                      </Avatar>

                      <Stack gap={2} style={{ flex: 1 }}>
                        <Text
                          size="xs"
                          c="dimmed"
                          style={{ letterSpacing: "0.3px", color: "#a5b4fc" }}
                        >
                          AI Agent Update
                        </Text>
                        <Text fw={500} size="sm" c="white">
                          {notification.message}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {notification.subtext}
                        </Text>
                      </Stack>

                      <ActionIcon
                        variant="subtle"
                        color="gray"
                        size="sm"
                        onClick={() => setNotification(null)}
                        style={{ opacity: 0.7 }}
                      >
                        <IconX size={14} />
                      </ActionIcon>
                    </Group>
                  </Paper>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Metrics Grid */}
            <Grid mb="md" style={{ flexShrink: 0 }}>
              {metrics.map((metric, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Paper
                    p="md"
                    radius="md"
                    style={{
                      background: "rgba(30, 30, 35, 0.5)",
                      border: "1px solid rgba(99, 102, 241, 0.1)",
                      boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
                      transition: "transform 0.2s ease, box-shadow 0.2s ease",
                      "&:hover": {
                        transform: "translateY(-2px)",
                        boxShadow: "0 6px 24px rgba(0, 0, 0, 0.3)",
                      },
                    }}
                  >
                    <Group justify="space-between" mb="xs">
                      <Text
                        size="xs"
                        c="dimmed"
                        style={{ letterSpacing: "0.3px" }}
                      >
                        {metric.label}
                      </Text>
                      <Box
                        style={{
                          background: "rgba(99, 102, 241, 0.1)",
                          padding: "4px",
                          borderRadius: "6px",
                        }}
                      >
                        {React.cloneElement(metric.icon as React.ReactElement, {
                          size: 14,
                        })}
                      </Box>
                    </Group>
                    <Group justify="space-between" align="flex-end">
                      <Text fw={500} size="sm" c="white">
                        {metric.value}
                      </Text>
                      <Badge
                        variant="light"
                        size="xs"
                        color={metric.change > 0 ? "indigo" : "red"}
                        styles={{
                          root: {
                            textTransform: "none",
                          },
                        }}
                      >
                        {metric.change > 0 ? "+" : ""}
                        {metric.change}%
                      </Badge>
                    </Group>
                  </Paper>
                </Grid.Col>
              ))}
            </Grid>

            {/* Activity Feeds */}
            <Flex gap="md" style={{ flex: 1, minHeight: 0 }}>
              {/* AI Activity Feed */}
              <Box style={{ flex: 2, overflow: "hidden" }}>
                <Paper
                  radius="md"
                  style={{
                    background: "rgba(30, 30, 35, 0.5)",
                    border: "1px solid rgba(99, 102, 241, 0.1)",
                    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <Box
                    px="md"
                    py="xs"
                    style={{
                      borderBottom: "1px solid rgba(255, 255, 255, 0.03)",
                      flexShrink: 0,
                    }}
                  >
                    <Group>
                      <Avatar
                        radius="xl"
                        size="sm"
                        style={{
                          background:
                            "linear-gradient(45deg, #6366f1, #8b5cf6)",
                        }}
                      >
                        <IconRobot size={14} style={{ color: "white" }} />
                      </Avatar>
                      <div>
                        <Text fw={500} size="xs" c="white">
                          AI Assistant
                        </Text>
                        <Text size="xs" c="dimmed">
                          Real-time Activity
                        </Text>
                      </div>
                    </Group>
                  </Box>
                  <Box p="md" style={{ flex: 1, overflowY: "auto" }}>
                    <AnimatePresence>
                      {aiActions.map((action, index) => (
                        <motion.div
                          key={`${action.id}-${Math.random()}`}
                          variants={itemVariants}
                          initial="hidden"
                          animate="visible"
                          exit={{ opacity: 0, y: 20 }}
                        >
                          <Paper
                            p="sm"
                            radius="md"
                            mb="xs"
                            style={{
                              background: "rgba(30, 30, 35, 0.4)",
                              border: "1px solid rgba(255, 255, 255, 0.05)",
                              transition: "transform 0.2s ease",
                              "&:hover": {
                                transform: "translateX(4px)",
                              },
                            }}
                          >
                            <Group justify="space-between" mb="xs">
                              <Group gap="sm">
                                <Box
                                  style={{
                                    background: "rgba(99, 102, 241, 0.1)",
                                    padding: "6px",
                                    borderRadius: "8px",
                                    color: "#6366f1",
                                  }}
                                >
                                  {React.cloneElement(
                                    action.icon as React.ReactElement,
                                    {
                                      size: 16,
                                    }
                                  )}
                                </Box>
                                <div>
                                  <Text fw={500} size="sm" c="white" mb={2}>
                                    {action.title}
                                  </Text>
                                  <Text size="xs" c="dimmed">
                                    {action.description}
                                  </Text>
                                </div>
                              </Group>
                              <Text
                                size="xs"
                                c="dimmed"
                                style={{ whiteSpace: "nowrap" }}
                              >
                                {action.timestamp}
                              </Text>
                            </Group>
                            {action.progress && (
                              <Progress
                                value={action.progress}
                                color="indigo"
                                size="xs"
                                radius="xl"
                                mt="xs"
                                style={{
                                  background: "rgba(255, 255, 255, 0.05)",
                                }}
                              />
                            )}
                          </Paper>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </Box>
                </Paper>
              </Box>

              {/* Active Tasks */}
              <Box style={{ flex: 1, overflow: "hidden" }}>
                <Paper
                  radius="md"
                  style={{
                    background: "rgba(30, 30, 35, 0.5)",
                    border: "1px solid rgba(99, 102, 241, 0.1)",
                    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <Box
                    p="sm"
                    style={{
                      borderBottom: "1px solid rgba(255, 255, 255, 0.05)",
                    }}
                  >
                    <Group justify="space-between">
                      <Text fw={500} size="sm" c="white">
                        Active Tasks
                      </Text>
                      <Badge variant="dot" color="indigo" size="xs">
                        3 In Progress
                      </Badge>
                    </Group>
                  </Box>

                  <Box
                    p="sm"
                    style={{
                      flex: 1,
                      overflowY: "auto",
                    }}
                  >
                    <Stack gap="xs">
                      {[
                        {
                          title:
                            userType === "creator"
                              ? "Brand Outreach"
                              : "Campaign Analysis",
                          progress: 75,
                          icon:
                            userType === "creator" ? (
                              <IconUsers size={16} />
                            ) : (
                              <IconChartLine size={16} />
                            ),
                          dueDate: "Today, 5:00 PM",
                        },
                        {
                          title:
                            userType === "creator"
                              ? "Content Planning"
                              : "Creator Matching",
                          progress: 45,
                          icon:
                            userType === "creator" ? (
                              <IconBulb size={16} />
                            ) : (
                              <IconTarget size={16} />
                            ),
                          dueDate: "Tomorrow, 10:00 AM",
                        },
                        {
                          title: "Performance Analysis",
                          progress: 90,
                          icon: <IconChartLine size={16} />,
                          dueDate: "Today, 3:00 PM",
                        },
                      ].map((task, index) => (
                        <Paper
                          key={index}
                          p="sm"
                          radius="md"
                          style={{
                            background: "rgba(30, 30, 35, 0.4)",
                            border: "1px solid rgba(255, 255, 255, 0.05)",
                            transition: "all 0.2s ease",
                            "&:hover": {
                              background: "rgba(35, 35, 40, 0.4)",
                              transform: "translateX(4px)",
                            },
                          }}
                        >
                          <Group justify="space-between" mb="xs">
                            <Group gap="sm">
                              <Box
                                style={{
                                  background: "rgba(99, 102, 241, 0.1)",
                                  padding: "6px",
                                  borderRadius: "8px",
                                  color: "#6366f1",
                                }}
                              >
                                {task.icon}
                              </Box>
                              <div>
                                <Text size="sm" fw={500} c="white">
                                  {task.title}
                                </Text>
                                <Text size="xs" c="dimmed">
                                  {task.dueDate}
                                </Text>
                              </div>
                            </Group>
                          </Group>
                          <Group justify="space-between" mt="xs">
                            <Progress
                              value={task.progress}
                              color="indigo"
                              size="xs"
                              radius="xl"
                              style={{
                                width: "80%",
                                background: "rgba(255, 255, 255, 0.05)",
                              }}
                            />
                            <Text size="xs" c="dimmed">
                              {task.progress}%
                            </Text>
                          </Group>
                        </Paper>
                      ))}
                    </Stack>
                  </Box>
                </Paper>
              </Box>
            </Flex>
          </Box>
        </Box>
      </Container>
    </div>
  );
}
