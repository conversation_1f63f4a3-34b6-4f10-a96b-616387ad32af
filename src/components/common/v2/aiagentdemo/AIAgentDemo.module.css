/* AIAgentDemo.module.css */
.aiAgentContainer {
  max-width: 90%;
  margin: 0 auto;
  border: 1px solid var(--mantine-color-dark-5);
  background-color: var(--mantine-color-dark-7);
  position: relative;
  overflow: hidden;
}

.aiAgentHeader {
  background-color: var(--mantine-color-dark-6);
  height: 70px;
  border-bottom: 1px solid var(--mantine-color-dark-5);
  display: flex;
  align-items: center;
}

.aiActionsPanel {
  background-color: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-5);
  padding: 20px;
}

.aiActionCard {
  background-color: var(--mantine-color-dark-7);
  border: 1px solid var(--mantine-color-dark-5);
  border-radius: var(--mantine-radius-md);
  padding: 15px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.aiActionCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(138, 111, 223, 0.1);
  border-color: var(--mantine-color-purple-5);
}

.aiCapabilitiesPanel {
  background-color: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-5);
  padding: 20px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.statusDot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #22c55e;
  animation: pulse 2s infinite ease-in-out;
}
