"use client";

import {
  Box,
  Container,
  Grid,
  Group,
  Paper,
  Stack,
  Text,
  Title,
  rem,
} from "@mantine/core";
import { motion, useScroll, useTransform } from "framer-motion";
import { NextPage } from "next";
import Head from "next/head";
import Link from "next/link";

import { COLECTA_APP_LOGIN_URL } from "@/components/common/constants/urls";
import { Footer } from "@/components/common/v2/footer/Footer";
import { Header } from "@/components/common/v2/header/Header";
import { FaGoogle } from "react-icons/fa";
import { Button } from "../button/Button";
import { HeroSection } from "../herosection/HeroSection";
import classes from "./CreatorLanding.module.css";

const features = [
  {
    title: "Smart Discovery",
    description:
      "AI-powered brand matching based on your unique style and audience",
    iconName: "search",
    colorAccent: "primary" as const,
  },
  // Add other features...
];

const steps = [
  {
    number: 1,
    title: "Sign Up",
    description: "Create your account and set up your profile.",
  },
  {
    number: 2,
    title: "Connect",
    description: "Link your social media accounts and preferences.",
  },
  {
    number: 3,
    title: "Collaborate",
    description: "Start receiving AI-powered brand recommendations.",
  },
];

const testimonials = [
  {
    quote:
      "My AI agent found me a perfect collaboration that I would have never discovered on my own. It even negotiated a rate 30% higher than I would have asked for!",
    name: "Jamie Chen",
    role: "Lifestyle Creator, 45K followers",
    avatar: "/avatars/testimonial1.jpg",
  },
  // Add other testimonials...
];

const CreatorLanding: NextPage = () => {
  const { scrollYProgress } = useScroll();
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);

  const fadeInUpVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <>
      <div className={classes.pageWrapper}>
        <Header />

        <main className={classes.mainContent}>
          <HeroSection userType="creator" />

          {/* How It Works Section */}
          <section id="how-it-works-section">
            <Box py={120}>
              <Container size="lg">
                <Stack align="center" mb={80}>
                  <Text className={classes.sectionLabel}>
                    Your AI Agent Journey
                  </Text>
                  <Title
                    order={2}
                    size={46}
                    ta="center"
                    className={classes.sectionTitle}
                  >
                    Meet Your{" "}
                    <span className={classes.gradientText}>
                      AI Agent Assistant
                    </span>
                  </Title>
                  <Text
                    size="md"
                    hiddenFrom="sm"
                    c="dimmed"
                    maw={600}
                    ta="center"
                  >
                    Your personal AI agent works 24/7 to find perfect brand
                    matches while you create
                  </Text>
                  <Text
                    size="xl"
                    visibleFrom="sm"
                    c="dimmed"
                    maw={600}
                    ta="center"
                  >
                    Your personal AI agent works 24/7 to find perfect brand
                    matches while you create
                  </Text>
                </Stack>

                <Grid gutter={32}>
                  {[
                    {
                      number: "01",
                      title: "Create Account",
                      description: "Quick signup with Google or email",
                    },
                    {
                      number: "02",
                      title: "Connect Platforms",
                      description: "Link your social media accounts",
                    },
                    {
                      number: "03",
                      title: "Set Preferences",
                      description: "Tell us about your content and style",
                    },
                    {
                      number: "04",
                      title: "Start Matching",
                      description: "Get AI-powered brand recommendations",
                    },
                  ].map((step, index) => (
                    <Grid.Col span={{ base: 12, sm: 6, md: 3 }} key={index}>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <div className={classes.step}>
                          <div className={classes.stepNumber}>
                            {step.number}
                          </div>
                          <div className={classes.stepContent}>
                            <Text className={classes.stepTitle}>
                              {step.title}
                            </Text>
                            <Text className={classes.stepDescription}>
                              {step.description}
                            </Text>
                          </div>
                        </div>
                      </motion.div>
                    </Grid.Col>
                  ))}
                </Grid>
              </Container>
            </Box>
          </section>

          {/* CTA Section */}
          <section className={classes.ctaSection} id="early-access-section">
            <Container size="lg">
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={{
                  hidden: { opacity: 0, y: 20 },
                  visible: { opacity: 1, y: 0 },
                }}
                transition={{ duration: 0.5 }}
              >
                <Paper className={classes.ctaWrapper}>
                  <Stack align="center" ta="center" gap="xl">
                    <Stack gap="xs">
                      <Text className={classes.sectionLabel}>
                        Ready to Start?
                      </Text>
                      <Title
                        order={1}
                        size={rem(46)}
                        className={classes.ctaTitle}
                      >
                        Get Your Personal{" "}
                        <span className={classes.gradientText}>AI Agent</span>{" "}
                        Today
                      </Title>
                    </Stack>
                    <Text size={rem(18)} maw={600} c="dimmed">
                      Join early adopters getting 100% free access to their AI
                      agents. Limited spots available.
                    </Text>
                    <Group gap="xs" mt="md">
                      <Link href={COLECTA_APP_LOGIN_URL} target="_blank">
                        <Button
                          size="xl"
                          variant="gradient"
                          gradientType="primary"
                          leftSection={<FaGoogle size={20} />}
                        >
                          Join with Google
                        </Button>
                      </Link>
                    </Group>
                    <Text size="sm" c="dimmed">
                      No credit card required • Cancel anytime
                    </Text>
                  </Stack>
                </Paper>
              </motion.div>
            </Container>
          </section>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default CreatorLanding;
