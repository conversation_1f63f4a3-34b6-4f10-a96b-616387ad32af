/* src/styles/CreatorLanding.module.css */
.pageWrapper {
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
  background: linear-gradient(
    135deg,
    rgba(30, 30, 35, 0.95),
    rgba(20, 20, 24, 0.95)
  );
}

.mainContent {
  position: relative;
  z-index: 1;
}

.section {
  padding: rem(80) 0;
  position: relative;
}

.sectionHeader {
  text-align: center;
  margin-bottom: rem(60);
}

.sectionTitle {
  font-size: rem(36);
  font-weight: 700;
  color: var(--mantine-color-dark-9);
  margin-bottom: rem(16);
}

.sectionSubtitle {
  font-size: rem(18);
  color: var(--mantine-color-gray-6);
  max-width: rem(600);
  margin: 0 auto;
}

.aiWorkflowSection {
  background: linear-gradient(180deg, var(--mantine-color-dark-7) 0%, var(--mantine-color-dark-8) 100%);
  position: relative;
  overflow: hidden;
}

.aiWorkflowSection::before,
.aiWorkflowSection::after {
  content: '';
  position: absolute;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.1;
  z-index: 0;
}

.aiWorkflowSection::before {
  background: var(--mantine-color-purple-6);
  top: -200px;
  right: -200px;
}

.aiWorkflowSection::after {
  background: var(--mantine-color-teal-6);
  bottom: -200px;
  left: -200px;
}

.workflowContent {
  position: relative;
  z-index: 1;
  text-align: center;
}

.workflowTitle {
  font-size: rem(48);
  font-weight: 700;
  letter-spacing: -0.5px;
  margin-bottom: rem(16);
  color: var(--mantine-color-white);
}

.workflowText {
  font-size: rem(20);
  color: var(--mantine-color-gray-5);
  max-width: rem(600);
  margin: 0 auto;
}

.aiDashboardPreview {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: rem(16);
  overflow: hidden;
  backdrop-filter: blur(12px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.dashboardHeader {
  padding: rem(20);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.aiAgentAvatar {
  width: rem(36);
  height: rem(36);
  padding: rem(8);
  border-radius: 50%;
  background: linear-gradient(135deg, #8a6fdf 0%, #64d9d0 100%);
  color: white;
}

.aiActivities {
  padding: rem(20);
  max-height: rem(400);
  overflow-y: auto;
}

.activityCard {
  padding: rem(16);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: rem(12);
  transition: all 0.2s ease;
}

.activityCard:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
}

.activityIcon {
  width: rem(40);
  height: rem(40);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: rem(10);
  background: rgba(255, 255, 255, 0.05);
  font-size: rem(20);
}

.featureCard {
  display: flex;
  gap: rem(20);
  padding: rem(24);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: rem(16);
  transition: all 0.2s ease;
}

.featureCard:hover {
  background: rgba(255, 255, 255, 0.04);
  transform: translateX(4px);
}

.featureIcon {
  width: rem(48);
  height: rem(48);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: rem(12);
  background: linear-gradient(135deg, rgba(138, 111, 223, 0.2) 0%, rgba(100, 217, 208, 0.2) 100%);
  font-size: rem(24);
}

.gradientText {
  background: linear-gradient(135deg, #8a6fdf 0%, #64d9d0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.aiAssistantContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: rem(40);
}

.aiAssistantWrapper {
  position: relative;
  width: rem(200);
  height: rem(200);
  display: flex;
  align-items: center;
  justify-content: center;
}

.aiAssistantIcon {
  position: relative;
  z-index: 2;
  color: #C4EE34;
}

.aiAssistantGlow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(196, 238, 52, 0.2) 0%, rgba(138, 111, 223, 0.1) 50%, transparent 70%);
  filter: blur(20px);
  border-radius: 50%;
}

.signupTitle {
  background: linear-gradient(135deg, var(--mantine-color-white) 0%, var(--mantine-color-gray-3) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.2;
}

.signupText {
  font-size: rem(18);
  color: var(--mantine-color-gray-7);
  margin-bottom: rem(32);
}

.benefitsList {
  display: flex;
  flex-direction: column;
  gap: var(--mantine-spacing-md);
}

.benefitItem {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-md);
}

.checkIcon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--mantine-color-primary-6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--mantine-color-white);
  font-size: 14px;
}

.ctaSection {
  padding: rem(80) 0;
  position: relative;
}

.ctaWrapper {
  background: rgba(30, 30, 35, 0.5);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  padding: rem(64) rem(32);
  border-radius: rem(24);
  position: relative;
  overflow: hidden;
}

.ctaWrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(138, 111, 223, 0.05) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  pointer-events: none;
}

.ctaTitle {
  font-weight: 600;
  letter-spacing: -0.5px;
  color: var(--mantine-color-white);
}

.ctaButton {
  background: #C4EE34;
  color: #000;
  font-weight: 500;
  padding: 0 3rem;
  height: 60px;
  transition: transform 0.2s ease;
}

.ctaButton:hover {
  transform: translateY(-2px);
  background: #d1f55a;
}

.heroSectionTitleContainer {
  backdrop-filter: drop-shadow(0px 40px #fcf8f7) blur(2px);
}

.stepsSection {
  background: #FAFAFA;
}

.sectionLabel {
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 1px;
  color: var(--mantine-color-purple-4);
  font-size: 14px;
}

.sectionTitle {
  color: var(--mantine-color-white);
  font-size: 42px;
  line-height: 1.2;
  margin-top: 12px;
}

.stepsContainer {
  max-width: 800px;
  margin: 0 auto;
}

.step {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
  background: rgba(30, 30, 35, 0.5);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  height: 100%;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
}

.step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(138, 111, 223, 0.03) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  pointer-events: none;
}

.step:hover {
  transform: translateY(-2px);
  background: rgba(35, 35, 40, 0.6);
  border-color: rgba(138, 111, 223, 0.2);
}

.stepNumber {
  font-size: 72px;
  font-weight: 700;
  color: var(--mantine-color-purple-4);
  line-height: 1;
  opacity: 0.12;
  position: absolute;
  top: 10px;
  right: 20px;
}

.stepContent {
  position: relative;
  z-index: 1;
}

.stepTitle {
  font-size: 20px;
  font-weight: 600;
  color: var(--mantine-color-white);
  margin-top: 16px;
}

.stepDescription {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.75);
  line-height: 1.5;
}

@media (max-width: $mantine-breakpoint-sm) {
  .step {
    padding: 20px;
  }
  
  .stepNumber {
    font-size: 56px;
  }
}

.previewContainer {
  margin-top: rem(20);
  padding: rem(20);
  background: rgba(255, 255, 255, 0.02);
  border-radius: rem(12);
  min-height: rem(100);
  display: flex;
  align-items: center;
  justify-content: center;
}

.socialButton {
  width: rem(48);
  height: rem(48);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: rem(12);
  cursor: pointer;
  transition: all 0.2s ease;
}

.socialButton:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.preferencesCard {
  width: 100%;
  padding: rem(16);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: rem(12);
}

.preferenceIcon {
  width: rem(32);
  height: rem(32);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: rem(8);
}

.activeAgentCard {
  width: 100%;
  padding: rem(24);
  background: rgba(138, 111, 223, 0.1);
  border: 1px solid rgba(138, 111, 223, 0.2);
  border-radius: rem(12);
}

.pulsingDot {
  width: rem(12);
  height: rem(12);
  background: #64d9d0;
  border-radius: 50%;
  position: relative;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(100, 217, 208, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(100, 217, 208, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(100, 217, 208, 0);
  }
}

.signupPreview,
.socialsPreview,
.preferencesPreview,
.agentPreview {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: rem(24);
  margin-top: rem(16);
  width: 100%;
}

.socialPlatform {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: rem(16);
  transition: all 0.3s ease;
  cursor: pointer;
}

.socialPlatform:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.agentAvatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulsingRing {
  position: absolute;
  width: rem(80);
  height: rem(80);
  border-radius: 50%;
  background: linear-gradient(135deg, var(--mantine-color-purple-5), var(--mantine-color-teal-5));
  opacity: 0.2;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.2;
  }
  70% {
    transform: scale(1.1);
    opacity: 0;
  }
  100% {
    transform: scale(0.95);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(5px);
  }
  60% {
    transform: translateY(3px);
  }
}

.accordionItem {
  background: var(--mantine-color-body);
  border: 1px solid var(--mantine-color-gray-2);
  margin-bottom: rem(8);
  transition: all 0.2s ease;
}

.accordionItem:hover {
  border-color: var(--mantine-color-purple-5);
}

.icon {
  transition: transform 0.2s ease;
}

.accordionControl[data-expanded] .icon {
  transform: rotate(180deg);
}

/* Responsive adjustments */
@media (max-width: $mantine-breakpoint-sm) {
  .stepCard {
    padding: rem(24);
  }
  
  .stepNumber {
    width: rem(40);
    height: rem(40);
    font-size: rem(18);
  }
}

@media (max-width: 768px) {
  .section {
    padding: rem(40) 0;
  }

  .sectionTitle {
    font-size: rem(28);
  }

  .sectionSubtitle {
    font-size: rem(16);
  }

  .aiWorkflowSection {
    padding: rem(60) 0;
  }

  .workflowTitle {
    font-size: rem(36);
  }

  .workflowText {
    font-size: rem(18);
  }

  .aiDashboardPreview {
    margin-bottom: rem(40);
  }

  .featureCard {
    padding: rem(20);
  }

  .aiAssistantWrapper {
    width: rem(160);
    height: rem(160);
  }

  .signupTitle {
    font-size: rem(24);
  }

  .signupText {
    font-size: rem(16);
  }

  .ctaTitle {
    font-size: 1.75rem;
  }
  
  .ctaWrapper {
    padding: 3rem 1.5rem;
  }
  
  .ctaWrapper > div {
    flex-direction: column;
    align-items: center;
    gap: var(--mantine-spacing-xl);
  }
}
