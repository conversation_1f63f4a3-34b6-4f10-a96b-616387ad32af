"use client";

import {
  <PERSON>,
  <PERSON>,
  Center,
  Container,
  Flex,
  Group,
  Image,
  Paper,
  rem,
  Transition,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { motion } from "framer-motion";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Button } from "../button/Button";
import classes from "./Header.module.css";

const HEADER_HEIGHT = 70;

const links = [
  { link: "/forcreators", label: "For Creators" },
  { link: "/forbrands", label: "For Brands" },
];

export function Header() {
  const router = useRouter();
  const pathname = usePathname();
  const [opened, { toggle, close }] = useDisclosure(false);

  const navigateToHome = () => {
    router.push("/");
  };

  const items = links.map((link) => (
    <Link
      key={link.label}
      href={link.link}
      className={
        pathname === link.link
          ? `${classes.link} ${classes.linkActive}`
          : classes.link
      }
      onClick={() => close()}
    >
      {link.label}
    </Link>
  ));

  return (
    <Box className={classes.root} h={HEADER_HEIGHT}>
      <Container size="lg" h="100%">
        <Flex justify="space-between" align="center" h="100%">
          <Link href="/forcreators" style={{ textDecoration: "none" }}>
            <motion.div
              className={classes.logo}
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 500, damping: 10 }}
            >
              <Image
                src="./colecta-logo.svg"
                w="100%"
                h={{ base: rem(32), md: rem(36) }}
                onClick={navigateToHome}
                style={{ cursor: "pointer" }}
              />
            </motion.div>
          </Link>

          <Group gap="md" visibleFrom="sm">
            {items}
          </Group>

          {/* <Group gap="md" visibleFrom="sm">
            <Button variant="subtle">Login</Button>
            <Button variant="gradient" gradientType="primary">
              Sign Up
            </Button>
          </Group> */}

          <Burger opened={opened} onClick={toggle} hiddenFrom="sm" />

          <Transition mounted={opened} transition="fade" duration={200}>
            {(styles) => (
              <Paper
                className={classes.dropdown}
                style={styles}
                withBorder
                p="md"
              >
                <Flex
                  direction="column"
                  gap="md"
                  style={{ textAlign: "center" }}
                >
                  {items}
                  <Button variant="subtle" fullWidth mt="md">
                    Login
                  </Button>
                  <Button variant="gradient" gradientType="primary" fullWidth>
                    Sign Up
                  </Button>
                </Flex>
              </Paper>
            )}
          </Transition>
        </Flex>
      </Container>
    </Box>
  );
}
