/* src/components/common/Header.module.css */
.root {
  position: relative;
  z-index: 100;
  background-color: transparent;
  border-bottom: none;
}

.dropdown {
  position: absolute;
  top: 70px;
  left: 0;
  right: 0;
  z-index: 0;
  overflow: hidden;
  background-color: var(--mantine-color-dark-6);
}

.link {
  display: block;
  line-height: 1;
  padding: 8px 12px;
  border-radius: var(--mantine-radius-sm);
  text-decoration: none;
  color: var(--mantine-color-dark-0);
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
  position: relative;
  transition: all 0.2s ease;
}

.link:hover {
  color: var(--mantine-color-purple-5);
}

.linkActive {
  color: var(--mantine-color-purple-5);
  position: relative;
}

.linkActive::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30%;
  height: 3px;
  border-radius: 2px;
  background-color: var(--mantine-color-purple-5);
}

.logo {
  font-size: 1.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #8a6fdf 20%, #64d9d0 70%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  letter-spacing: -0.5px;
}