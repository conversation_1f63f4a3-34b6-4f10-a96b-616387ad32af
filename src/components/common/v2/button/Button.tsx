"use client";

import {
  ButtonProps,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tineGradient,
} from "@mantine/core";
import { motion } from "framer-motion";

interface GradientButtonProps extends ButtonProps {
  gradientType?: "primary" | "secondary" | "accent";
}

// Create a motion component from MantineButton
import { forwardRef } from "react";

const ForwardedMantineButton = forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => <MantineButton ref={ref} {...props} />
);

const MotionButton = motion(ForwardedMantineButton);

export function Button({
  children,
  gradientType = "primary",
  variant = "filled",
  size = "md",
  ...props
}: GradientButtonProps) {
  // Determine custom gradient based on prop
  let customGradient: MantineGradient | undefined;

  if (variant === "gradient") {
    switch (gradientType) {
      case "primary":
        customGradient = { from: "purple.5", to: "purple.8", deg: 45 };
        break;
      case "secondary":
        customGradient = { from: "teal.5", to: "teal.8", deg: 45 };
        break;
      case "accent":
        customGradient = { from: "#FF9B82", to: "#FFD98F", deg: 45 };
        break;
      default:
        customGradient = undefined;
    }
  }

  return (
    <MotionButton
      variant={variant}
      size={size}
      gradient={customGradient}
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      {...{ ...props, style: undefined }}
    >
      {children}
    </MotionButton>
  );
}
