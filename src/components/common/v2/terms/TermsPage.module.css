.pageWrapper {
  position: relative;
  background: linear-gradient(0deg,
      rgba(30, 30, 35, 0.95),
      rgba(20, 20, 24, 0.95));
}

.titleSection {
  text-align: center;
  padding: 5rem;
  background: linear-gradient(0deg,
      rgba(77, 51, 180),
      rgba(30, 30, 35));
  border-radius: 0 0 60% 60%;
}

.title {
  font-size: 3.125rem;
}

.span {
  font-weight: 600;
  color: #C3ADFF;
}

.link {
  color: #876bc2;
  text-decoration: none;
}

.link:hover {
  color: #C3ADFF;
  text-decoration: underline;
}

@media screen and (max-width: 780px) {
  .titleSection {
    padding: 3rem;
  }

  .title {
    font-size: 1.75rem;
  }

  .contentTitle {
    font-size: 1.5rem;
  }
}