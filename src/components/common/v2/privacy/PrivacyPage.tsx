"use client";

import { NextPage } from "next";
import { Header } from "../header/Header";
import classes from "./PrivacyPage.module.css";
import { Box, Divider, List, rem, Stack, Text, Title } from "@mantine/core";
import { motion } from "framer-motion";
import { Footer } from "../footer/Footer";

const PrivacyPage: NextPage = () => {
  const PrivacySections = [
    {
      title: "Scope of This Policy",
      description: "This policy applies to:",
      points: [
        "All users of our platform: creators, brands, and visitors",
        "Data collected through our website, application, and integrated social media platforms (Instagram, YouTube)",
        "Data collected through communications, transactions, and user activity",
      ],
    },
    {
      title: "Information We Collect",
      description: "We collect various types of data from and about users:",
      subsections: [
        {
          title: "a. Personal Identification Information",
          points: [
            "Full name, username, email address, phone number",
            "Business or brand name (for brand users)",
          ],
        },
        {
          title: "b. Profile and Account Information",
          points: [
            "Profile pictures, bios, service offerings, rates",
            "Past collaborations, niche details, content categories",
          ],
        },
        {
          title: "c. Social Media Information",
          description:
            "Upon authorization, we access data from Instagram and YouTube:",
          points: [
            "Follower count, engagement metrics, post performance",
            "Publicly available videos, reels, stories, and captions",
            "Channel metadata, audience demographics, performance reports",
          ],
        },
        {
          title: "d. Financial Information",
          points: [
            "Bank account and payout details (used solely for transferring earnings)",
            "Tax-related information (if applicable)",
            "Transaction history and invoices generated via the platform",
          ],
        },
        {
          title: "e. Collaboration and Communication Data",
          points: [
            "Campaign applications, proposal submissions, agreements",
            "Uploaded content, feedback, messages between creators and brands",
            "Deadlines, approvals, and revision statuses",
          ],
        },
        {
          title: "f. Usage & Technical Information",
          points: [
            "IP address, browser type, device type, OS",
            "Time spent, pages visited, clicks, navigation patterns",
            "Log files and error reports",
          ],
        },
        {
          title: "g. Cookies and Similar Technologies",
          points: [
            "Cookies to remember user sessions, preferences, and login status",
            "Tools like Google Analytics to track usage and platform optimization",
            "Pixel tags and scripts for functionality and analytics",
          ],
        },
      ],
    },
    {
      title: "How We Use Your Data",
      description: "We use the collected data to:",
      subsections: [
        {
          title: "a. Deliver and Improve Our Services",
          points: [
            "Connect creators with matching brand opportunities",
            "Enhance recommendations using AI",
            "Provide personalized dashboard insights and tools",
          ],
        },
        {
          title: "b. Ensure Platform Functionality",
          points: [
            "Facilitate campaign workflows, messaging, and submissions",
            "Process transactions and manage payout systems",
            "Send notifications, reminders, and updates",
          ],
        },
        {
          title: "c. Optimize User Experience",
          points: [
            "Improve platform layout, performance, and content",
            "Use analytics to troubleshoot issues or refine features",
            "Test and deploy machine learning models",
          ],
        },
        {
          title: "d. Marketing & Communication",
          points: [
            "Send important service-related emails (campaigns, approvals, payment updates)",
            "Notify users of new features, opportunities, or updates (opt-out available)",
          ],
        },
      ],
    },
    {
      title: "Legal Basis for Data Processing",
      description: "As an Indian company, we process your data:",
      points: [
        "With your explicit or implied consent (e.g., when you sign up or connect social accounts)",
        "To perform our contractual obligations (e.g., manage payouts, campaigns)",
        "To comply with legal requirements (e.g., accounting, fraud prevention)",
      ],
    },
    {
      title: "Data Retention",
      points: [
        "For as long as your account is active",
        "As needed to provide services and fulfill obligations",
        "As legally required (e.g., payment records, invoices)",
        "Users may request account deletion, after which your data will be permanently deleted or anonymized — unless retention is legally required.",
      ],
    },
    {
      title: "Data Sharing and Disclosure",
      points: [
        "We do not sell, rent, or trade your personal data.",
        "We may share your information only:",
        "With trusted service providers (e.g., payment processors, analytics tools) under strict confidentiality agreements",
        "To comply with Indian legal or regulatory requests",
        "To investigate or respond to fraud, abuse, or platform misuse",
        "No third-party marketing or advertising entities currently have access to your data.",
      ],
    },
    {
      title: "Data Security",
      points: [
        "Encrypted storage and communication (HTTPS, secure databases)",
        "Access control protocols for sensitive information",
        "Regular vulnerability scans and monitoring",
        "However, no system is 100% secure. In the event of a data breach, we will promptly notify affected users and authorities as required by law.",
      ],
    },
    {
      title: "Use of Artificial Intelligence (AI)",
      points: [
        "Analyze creator profiles and content performance",
        "Recommend optimal collaborations or pricing insights",
        "Predict campaign outcomes and assist in decision-making",
        "All AI-driven features operate on responsibly collected and secured data, with no unsolicited automation or decision-making without user awareness.",
      ],
    },
    {
      title: "International Users",
      points: [
        "Currently, we serve users within India. In the future, as we expand globally, we will comply with applicable data protection laws like the GDPR (EU), CCPA (US), or others.",
        "We will update this policy and notify you when those legal frameworks become applicable.",
      ],
    },
    {
      title: "Your Rights and Choices",
      points: [
        "Access your personal data",
        "Correct or update inaccuracies",
        "Delete your data or request account removal",
        "Withdraw consent for optional data processing",
        "Opt out of marketing emails",
        "To exercise these rights, email us at [<EMAIL>]. We will respond within a reasonable timeframe.",
      ],
    },
    {
      title: "Children's Privacy",
      points: [
        "Our platform is intended for users aged 18 and above.",
        "We do not knowingly collect personal data from children under 18.",
        "If we discover such data, we will delete it immediately.",
      ],
    },
    {
      title: "Changes to This Privacy Policy",
      points: [
        "We may update this Privacy Policy from time to time to reflect changes in law, technology, or our business practices.",
        "We will notify users of any significant updates through:",
        "In-app notification, and/or",
        "Email communication",
        "Your continued use of the platform constitutes acceptance of the updated policy.",
      ],
    },
    {
      title: "Contact Us",
      points: [
        "Email: <EMAIL>",
        "Company: Colecta AI Pvt. Ltd.",
        "Address: FLAT NO-604 DK-4 LEELA, ATULYAM MISROD, Huzur, Madhya Pradesh, 462039, Trilanga, Bhopal, India",
      ],
    },
  ];

  return (
    <>
      <div className={classes.pageWrapper}>
        <Header />
      </div>
      <Box className={classes.titleSection}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Title className={classes.title}>Privacy Policy</Title>
        </motion.div>
      </Box>
      <Box
        component="section"
        px={{ base: rem(30), md: rem(100), lg: rem(250) }}
        py={rem(60)}
        className={classes.content}
      >
        <Stack
          gap={0}
          style={{
            marginTop: rem(15),
            marginLeft: rem(4),
            marginBottom: rem(30),
          }}
        >
          <Text fw={700}>Effective Date: May 30, 2025</Text>
          <Text fw={700}> Last Updated: May 30, 2025</Text>
        </Stack>
        <Stack>
          <div>
            <Text>
              At{" "}
              <Text span className={classes.span}>
                Colecta AI Pvt. Ltd.{" "}
              </Text>
              ("we", "us", "our", or "Colecta AI"), your privacy is of paramount
              importance. This Privacy Policy describes how we collect, use,
              store, and protect your personal information when you use our
              website, mobile platform, and services (collectively, the
              "Platform"). By accessing or using Colecta AI, you agree to the
              practices described in this policy.
            </Text>
            <Text> If you do not agree, please do not use the Platform.</Text>
          </div>
          <Divider my="lg" />
          <Title order={1} className={classes.contentTitle}>
            About Us
          </Title>
          <div>
            <Text fw={500}>Colecta AI Pvt. Ltd. </Text>
            <Text>
              Address: FLAT NO-604 DK-4 LEELA, ATULYAM MISROD, Huzur, Madhya
              Pradesh, 462039, Trilanga, Bhopal, India
            </Text>
            <Text>
              Email:{" "}
              <Text component="a" href="mailto:<EMAIL>">
                <EMAIL>
              </Text>
            </Text>
          </div>
          <Text>
            We are an AI-powered collaboration and management platform that
            helps content creators connect with brands, manage their campaigns,
            track deliverables, and get paid efficiently.
          </Text>
          {PrivacySections.map((section, idx) => (
            <div key={idx}>
              <Divider my="md" />
              <div key={idx}>
                <Title order={1} mb="xs" className={classes.contentTitle}>
                  {section.title}
                </Title>
                {section.description && (
                  <Text size="md" mb="sm">
                    {section.description}
                  </Text>
                )}

                {/* If section has direct points */}
                {section.points && (
                  <List spacing="xs" listStyleType="disc" withPadding>
                    {section.points.map((point, i) => (
                      <List.Item key={i}>{point}</List.Item>
                    ))}
                  </List>
                )}

                {/* If section has nested subsections */}
                {section.subsections &&
                  section.subsections.map((sub, j) => (
                    <div key={j} style={{ marginTop: 16 }}>
                      <Text fw={600} pb={5}>
                        {sub.title}
                      </Text>
                      {sub.description && (
                        <Text size="sm" mt={4} mb={4}>
                          {sub.description}
                        </Text>
                      )}
                      <List spacing="xs" listStyleType="disc" withPadding>
                        {sub.points.map((point, k) => (
                          <List.Item key={k}>{point}</List.Item>
                        ))}
                      </List>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </Stack>
      </Box>
      <Footer />
    </>
  );
};

export default PrivacyPage;
