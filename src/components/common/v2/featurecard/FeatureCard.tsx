"use client";

import { Paper, Text, Title } from "@mantine/core";
import {
  IconCash,
  IconChartLine,
  IconHeartHandshake,
  IconProps,
  IconRobot,
  IconSearch,
  IconWand,
} from "@tabler/icons-react";
import { motion } from "framer-motion";
import classes from "./FeatureCard.module.css";

interface FeatureCardProps {
  title: string;
  description: string;
  iconName: string;
  colorAccent: "primary" | "secondary" | "accent";
  hoverable?: boolean;
}

export const FeatureCard = ({
  title,
  description,
  iconName,
  colorAccent,
  hoverable = false,
}: FeatureCardProps) => {
  // Map string names to Tabler icons
  const icons: { [key: string]: React.ComponentType<IconProps> } = {
    search: IconSearch,
    chartLine: IconChartLine,
    moneyBill: IconCash,
    magic: IconWand,
    handshake: IconHeartHandshake,
    robot: IconRobot,
  };

  const IconComponent = icons[iconName] || IconSearch;

  return (
    <motion.div
      whileHover={hoverable ? { y: -5 } : {}}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      <Paper
        className={`${classes.card} ${hoverable ? classes.hoverable : ""}`}
        radius="md"
      >
        <div
          className={`${classes.iconWrapper} ${classes[`icon${colorAccent}`]}`}
        >
          <IconComponent size={28} stroke={1.5} className={classes.icon} />
        </div>

        <div className={classes.content}>
          <Title order={3} className={classes.title}>
            {title}
          </Title>
          <Text className={classes.description}>{description}</Text>
        </div>

        <div className={classes.shine} />
      </Paper>
    </motion.div>
  );
};
