/* src/components/common/v2/featurecard/FeatureCard.module.css */

.card {
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: rem(32);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  overflow: hidden;
}

.hoverable {
  transition: all 0.3s ease;
}

.hoverable:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.2);
}

.iconWrapper {
  position: relative;
  width: rem(56);
  height: rem(56);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: rem(24);
  transition: all 0.3s ease;
}

.iconprimary {
  background: linear-gradient(135deg, rgba(138, 111, 223, 0.1), rgba(180, 105, 255, 0.1));
  border: 1px solid rgba(138, 111, 223, 0.2);
  color: var(--mantine-color-violet-4);
}

.iconsecondary {
  background: linear-gradient(135deg, rgba(100, 217, 208, 0.1), rgba(79, 251, 235, 0.1));
  border: 1px solid rgba(100, 217, 208, 0.2);
  color: var(--mantine-color-cyan-4);
}

.iconaccent {
  background: linear-gradient(135deg, rgba(255, 155, 130, 0.1), rgba(255, 217, 143, 0.1));
  border: 1px solid rgba(255, 155, 130, 0.2);
  color: var(--mantine-color-orange-4);
}

.icon {
  transition: transform 0.3s ease;
}

.hoverable:hover .icon {
  transform: scale(1.1);
}

.content {
  position: relative;
  z-index: 1;
}

.title {
  font-size: var(--mantine-font-size-lg);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: rem(12);
  color: var(--mantine-color-white);
}

.description {
  font-size: var(--mantine-font-size-sm);
  line-height: 1.6;
  color: var(--mantine-color-dimmed);
  margin: 0;
}

.shine {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  transition: transform 0.7s ease;
  pointer-events: none;
}

.hoverable:hover .shine {
  transform: translateX(200%) skewX(-20deg);
}

@media (max-width: 768px) {
  .card {
    padding: rem(24);
  }
  
  .iconWrapper {
    width: rem(48);
    height: rem(48);
    margin-bottom: rem(20);
  }
  
  .title {
    font-size: var(--mantine-font-size-md);
  }
}
