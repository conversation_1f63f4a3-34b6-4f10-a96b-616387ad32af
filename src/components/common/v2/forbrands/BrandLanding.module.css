/* src/styles/BrandLanding.module.css */

.pageWrapper {
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
  background: linear-gradient(135deg,
      rgba(30, 30, 35, 0.95),
      rgba(20, 20, 24, 0.95));
}

.mainContent {
  position: relative;
  z-index: 1;
}

.sectionLabel {
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 1px;
  color: var(--mantine-color-purple-4);
  font-size: 14px;
}

/* Hero Section */
.heroSection {
  padding: 100px 0 80px;
  background-color: #1e1e1e;
  position: relative;
  overflow: hidden;
}

.heroTitle {
  font-size: 3.5rem;
  line-height: 1.2;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
}

.gradientText {
  background: linear-gradient(135deg, #8a6fdf 0%, #64d9d0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroText {
  color: #b0b0b0;
  max-width: 550px;
  line-height: 1.6;
}

/* Metrics Section */
.metricsSection {
  padding: rem(100) 0;
  background: linear-gradient(180deg,
      rgba(20, 20, 24, 0.95) 0%,
      rgba(30, 30, 35, 0.95) 100%);
}

.metricsHeader {
  max-width: rem(800);
  margin: 0 auto rem(60);
}

.metricsTitle {
  font-size: rem(42);
  line-height: 1.2;
  font-weight: 700;
  text-align: center;
  color: var(--mantine-color-white);
}

.metricsRow {
  display: flex;
  gap: rem(16);
  /* overflow-x: auto; */
  padding: rem(4);
  margin: 0 rem(-16);
  padding: 0 rem(16);
  flex-wrap: wrap;

  /* Hide scrollbar but keep functionality */
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.metricCard {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: rem(20);
  padding: rem(24);
  min-width: rem(240);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metricBackground {
  position: absolute;
  top: 50%;
  right: rem(-10);
  transform: translateY(-50%);
  font-size: rem(140);
  font-weight: 800;
  opacity: 0.02;
  /* Reduced from 0.04 to 0.02 */
  pointer-events: none;
  color: var(--mantine-color-white);
  white-space: nowrap;
  line-height: 1;
  z-index: 0;
}

.metricContent {
  position: relative;
  z-index: 1;
}

.metricIcon {
  position: relative;
  z-index: 1;
  margin-bottom: rem(20);
}

.metricHeader {
  display: flex;
  align-items: baseline;
  gap: rem(8);
  margin-bottom: rem(8);
}

.metricValue {
  font-size: rem(64);
  font-weight: 700;
  background: linear-gradient(135deg,
      var(--mantine-color-blue-4) 0%,
      var(--mantine-color-violet-4) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metricTrend {
  font-size: rem(13);
  color: var(--mantine-color-dimmed);
  font-weight: 500;
}

.metricLabel {
  font-size: rem(16);
  font-weight: 600;
  color: var(--mantine-color-white);
  margin-bottom: rem(4);
}

.metricDescription {
  font-size: rem(13);
  color: var(--mantine-color-gray-5);
  line-height: 1.5;
}

@media (max-width: $mantine-breakpoint-md) {
  .metricsTitle {
    font-size: rem(32);
  }

  .metricsRow {
    /* flex-wrap: wrap; */
    margin: 0 rem(-24);
    padding: 0 rem(24);
  }

  .metricCard {
    min-width: rem(220);
  }

  .metricValue {
    font-size: rem(48);
  }

  .metricBackground {
    font-size: rem(90);
  }
}

/* Features Section */
.featuresSection {
  padding: 80px 0;
  background-color: #121212;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
}

/* How It Works Section */
.howItWorksSection {
  padding: 80px 0;
  background-color: #1e1e1e;
}

/* Steps Section Styles */
.stepsContainer {
  padding: rem(20);
}

.step {
  display: flex;
  gap: rem(24);
  padding: rem(24) rem(12);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.step:last-child {
  border-bottom: none;
}

.stepNumber {
  font-size: rem(32);
  font-weight: 700;
  color: var(--mantine-color-purple-4);
  opacity: 0.8;
  min-width: rem(48);
  text-align: center;
}

.stepContent {
  flex: 1;
}

.stepTitle {
  color: var(--mantine-color-white);
  font-size: rem(24);
  font-weight: 600;
  margin-bottom: rem(8);
}

/* Animation Container Styles */
.animationContainer {
  position: relative;
  height: rem(400);
  display: flex;
  align-items: center;
  justify-content: center;
}

.glowCircle {
  position: absolute;
  width: rem(300);
  height: rem(300);
  border-radius: 50%;
  background: radial-gradient(circle,
      rgba(138, 111, 223, 0.2) 0%,
      rgba(100, 217, 208, 0.1) 100%);
  filter: blur(40px);
}

.robotFigure {
  position: relative;
  z-index: 2;
}

.robotIcon {
  color: var(--mantine-color-purple-4);
  filter: drop-shadow(0 0 20px rgba(138, 111, 223, 0.4));
}

.glowRing {
  position: absolute;
  width: rem(340);
  height: rem(340);
  border-radius: 50%;
  border: 2px solid var(--mantine-color-purple-4);
  opacity: 0.3;
}

/* Case Studies Section */
.caseStudiesSection {
  padding: 80px 0;
  background-color: #121212;
}

.caseStudyCard {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #2c2c2c;
}

.caseStudyCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(138, 111, 223, 0.1);
}

.caseStudyBrand {
  color: #8a6fdf;
}

.caseStudyResult {
  color: #b0b0b0;
}

.caseStudyQuote {
  flex: 1;
  position: relative;
  padding-left: 20px;
  border-left: 3px solid #8a6fdf;
  font-style: italic;
  color: #b0b0b0;
}

/* Sign Up Section */
.signupSection {
  background: linear-gradient(135deg,
      rgba(30, 30, 35, 0.95),
      rgba(20, 20, 24, 0.95));
  position: relative;
}

.signupTitle {
  font-size: rem(46);
  line-height: 1.2;
  font-weight: 700;
  color: var(--mantine-color-white);
}

.signupText {
  color: var(--mantine-color-gray-4);
  line-height: 1.6;
}

.featureItem {
  padding: rem(16);
  border-radius: var(--mantine-radius-md);
  background: rgba(255, 255, 255, 0.03);
  transition: all 0.3s ease;
}

.featureItem:hover {
  background: rgba(255, 255, 255, 0.06);
  transform: translateY(-2px);
}

.featureIcon {
  font-size: rem(24);
  width: rem(40);
  height: rem(40);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--mantine-radius-md);
  background: rgba(var(--mantine-color-purple-4), 0.1);
}

.formWrapper {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: $mantine-breakpoint-sm) {
  .signupTitle {
    font-size: rem(36);
  }

  .featureItem {
    padding: rem(12);
  }

  .featureIcon {
    width: rem(32);
    height: rem(32);
    font-size: rem(20);
  }
}

/* FAQ Section */
.faqSection {
  padding: 80px 0;
  background-color: #121212;
}

.faqCard {
  border: 1px solid #2c2c2c;
  background-color: #1e1e1e;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.faqCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(138, 111, 223, 0.1);
}

/* CTA Section */
.ctaSection {
  padding: rem(80) 0;
  position: relative;
}

.ctaWrapper {
  background: rgba(30, 30, 35, 0.5);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  padding: rem(64) rem(32);
  border-radius: rem(24);
  position: relative;
  overflow: hidden;
}

.ctaWrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(138, 111, 223, 0.05) 0%,
      rgba(0, 0, 0, 0) 100%);
  pointer-events: none;
}

.ctaTitle {
  font-weight: 600;
  letter-spacing: -0.5px;
  color: var(--mantine-color-white);
}

@media (max-width: $mantine-breakpoint-sm) {
  .ctaSection {
    padding: rem(40) 0;
  }

  .ctaWrapper {
    padding: rem(40) rem(24);
  }

  .ctaTitle {
    font-size: rem(32);
  }
}

/* Media Queries */
@media (max-width: 768px) {
  .heroSection {
    padding: 60px 0 40px;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .metricValue {
    font-size: 2rem;
  }

  .sectionTitle,
  .signupTitle,
  .ctaTitle {
    font-size: 2rem;
  }

  .step {
    margin-bottom: 30px;
  }

  .stepNumber {
    font-size: 1.75rem;
    width: 40px;
  }
}

/* Media Queries */
@media (max-width: $mantine-breakpoint-sm) {
  .step {
    padding: rem(16) rem(8);
    gap: rem(16);
  }

  .stepNumber {
    font-size: rem(24);
    min-width: rem(36);
  }

  .stepTitle {
    font-size: rem(20);
  }

  .animationContainer {
    height: rem(300);
  }

  .glowCircle {
    width: rem(240);
    height: rem(240);
  }

  .glowRing {
    width: rem(260);
    height: rem(260);
  }

  .robotIcon {
    width: rem(100);
    height: rem(100);
  }
}

.signupSection {
  padding: calc(var(--mantine-spacing-xl) * 3) 0;
  background: linear-gradient(to bottom,
      rgba(var(--mantine-color-dark-7), 0.5),
      rgba(var(--mantine-color-dark-8), 0.5));
  border-top: 1px solid var(--mantine-color-dark-4);
  border-bottom: 1px solid var(--mantine-color-dark-4);
}

.preTitle {
  font-size: rem(14px);
  font-weight: 600;
  letter-spacing: 0.1em;
  margin-top: calc(var(--mantine-spacing-xl));
  color: var(--mantine-color-purple-5);
}

.signupTitle {
  font-size: rem(42px);
  line-height: 1.2;
  font-weight: 800;

  @media (max-width: $mantine-breakpoint-sm) {
    font-size: rem(32px);
  }
}

.gradientText {
  background: linear-gradient(45deg,
      var(--mantine-color-purple-4),
      var(--mantine-color-purple-6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.signupText {
  color: var(--mantine-color-gray-5);
  max-width: 500px;
  line-height: 1.6;
}

.benefitsCard {
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.1);
  padding: var(--mantine-spacing-xl);
  border-radius: var(--mantine-radius-lg);
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--mantine-spacing-lg);
}

.benefitItem {
  padding: var(--mantine-spacing-md);
  border-radius: var(--mantine-radius-md);
  background: rgba(255, 255, 255, 0.03);
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.05);
  }
}

.formWrapper {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);

  @media (max-width: $mantine-breakpoint-sm) {
    margin: 0 -var(--mantine-spacing-md);
    border-radius: 0;
  }
}