"use client";
import {
  Box,
  Container,
  Grid,
  Group,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Title,
  rem,
} from "@mantine/core";
import { motion } from "framer-motion";
import { NextPage } from "next";
import Head from "next/head";

import { FeatureCard } from "@/components/common/v2/featurecard/FeatureCard";
import { Footer } from "@/components/common/v2/footer/Footer";
import { Header } from "@/components/common/v2/header/Header";
import { SignupForm } from "@/components/common/v2/signupform/SignupForm";

import {
  IconArrowUpRight,
  IconChartBar,
  IconClockHour4,
  IconTarget,
  IconUsers,
} from "@tabler/icons-react";
import { FaRobot } from "react-icons/fa";
import { HeroSection } from "../herosection/HeroSection";
import classes from "./BrandLanding.module.css";

const BrandLanding: NextPage = () => {
  return (
    <>
      <div className={classes.pageWrapper}>
        <Header />

        <main className={classes.mainContent}>
          <HeroSection userType="brand" />

          {/* Platform Metrics Section */}
          <Box component="section" className={classes.metricsSection}>
            <Container size="xl">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <Stack align="center" mb={80}>
                  <Text className={classes.sectionLabel}>Platform Metrics</Text>
                  <Title ta="center" className={classes.sectionTitle}>
                    Transforming Brand Campaigns with
                    <br />
                    <Text
                      component="span"
                      className={classes.gradientText}
                      size={rem(56)}
                    >
                      AI-Powered
                    </Text>{" "}
                    Intelligence
                  </Title>
                  {/* MOBILE VIEW for Text */}
                  <Text size="md" c="dimmed" ta="center" hiddenFrom="sm">
                    Your personal AI agent works tirelessly to find the perfect
                    creators and maximize ROI
                  </Text>
                  {/* DESKTOP VIEW View for Text*/}
                  <Text
                    size="xl"
                    c="dimmed"
                    maw={800}
                    ta="center"
                    style={{ whiteSpace: "nowrap" }}
                    visibleFrom="sm"
                  >
                    Your personal AI agent works tirelessly to find the perfect
                    creators and maximize ROI
                  </Text>
                </Stack>
              </motion.div>

              <div className={classes.metricsRow}>
                {[
                  {
                    metric: "42%",
                    trend: "+12% this quarter",
                    label: "Engagement Increase",
                    description: "Higher engagement across platforms",
                    icon: <IconChartBar size={28} stroke={1.5} />,
                    color: "var(--mantine-color-blue-5)",
                  },
                  {
                    metric: "2.8x",
                    trend: "Industry leading",
                    label: "ROI Multiplier",
                    description: "Compared to traditional methods",
                    icon: <IconArrowUpRight size={28} stroke={1.5} />,
                    color: "var(--mantine-color-violet-5)",
                  },
                  {
                    metric: "68%",
                    trend: "Time saved",
                    label: "Faster Discovery",
                    description: "Reduced discovery & vetting time",
                    icon: <IconClockHour4 size={28} stroke={1.5} />,
                    color: "var(--mantine-color-yellow-5)",
                  },
                  {
                    metric: "37%",
                    trend: "Consistent growth",
                    label: "Conversion Boost",
                    description: "Higher campaign conversion rates",
                    icon: <IconTarget size={28} stroke={1.5} />,
                    color: "var(--mantine-color-green-5)",
                  },
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    style={{ flex: 1 }}
                  >
                    <Paper className={classes.metricCard}>
                      <div
                        className={classes.metricIcon}
                        style={{ color: item.color }}
                      >
                        {item.icon}
                      </div>
                      <div className={classes.metricContent}>
                        <div className={classes.metricHeader}>
                          <Text className={classes.metricValue}>
                            {item.metric}
                          </Text>
                          <Text className={classes.metricTrend}>
                            {item.trend}
                          </Text>
                        </div>
                        <Text className={classes.metricLabel}>
                          {item.label}
                        </Text>
                        <Text className={classes.metricDescription}>
                          {item.description}
                        </Text>
                      </div>
                      <div
                        className={classes.metricBackground}
                        aria-hidden="true"
                      >
                        {item.metric}
                      </div>
                    </Paper>
                  </motion.div>
                ))}
              </div>
            </Container>
          </Box>

          {/* Features Section */}
          <Box component="section" className={classes.featuresSection}>
            <Container size="lg">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <Stack align="center" mb={80}>
                  <Text className={classes.sectionLabel}>
                    Platform Features
                  </Text>
                  <Title ta="center" className={classes.sectionTitle}>
                    Supercharge Your Creator Marketing
                    <br />
                    <Text
                      component="span"
                      className={classes.gradientText}
                      size={rem(72)}
                    >
                      AI-Powered
                    </Text>{" "}
                    Results
                  </Title>
                  {/* MOBILE VIEW for Text */}
                  <Text size="md" c="dimmed" ta="center" hiddenFrom="sm">
                    Your AI agent automates discovery, optimization, and
                    delivers predictable ROI
                  </Text>
                  {/*DESKTOP VIEW for Text */}
                  <Text
                    size="xl"
                    c="dimmed"
                    maw={800}
                    ta="center"
                    style={{ whiteSpace: "nowrap" }}
                    visibleFrom="sm"
                  >
                    Your AI agent automates discovery, optimization, and
                    delivers predictable ROI
                  </Text>
                </Stack>
              </motion.div>

              <Grid gutter={32}>
                {[
                  {
                    title: "Smart Creator Matching",
                    description:
                      "Find creators who truly align with your brand values and audience",
                    icon: "search",
                    colorAccent: "primary" as "primary",
                  },
                  {
                    title: "Performance Prediction",
                    description:
                      "Get accurate forecasts of engagement and conversions before investing",
                    icon: "chartLine",
                    colorAccent: "secondary" as "secondary",
                  },
                  {
                    title: "Budget Optimization",
                    description:
                      "Optimize budget allocation across creators to maximize campaign ROI",
                    icon: "moneyBill",
                    colorAccent: "accent" as "accent",
                  },
                  {
                    title: "Content Strategy AI",
                    description:
                      "Data-driven recommendations for content that resonates with audiences",
                    icon: "magic",
                    colorAccent: "primary" as "primary",
                  },
                  {
                    title: "Automated Contracting",
                    description:
                      "AI-powered contract drafting based on industry standards",
                    icon: "handshake",
                    colorAccent: "secondary" as "secondary",
                  },
                  {
                    title: "Continuous Learning",
                    description:
                      "Your AI agent gets smarter with each campaign to improve results",
                    icon: "robot",
                    colorAccent: "accent" as "accent",
                  },
                ].map((feature, index) => (
                  <Grid.Col span={{ base: 12, sm: 6, md: 4 }} key={index}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <FeatureCard
                        title={feature.title}
                        description={feature.description}
                        iconName={feature.icon}
                        colorAccent={feature.colorAccent}
                        hoverable
                      />
                    </motion.div>
                  </Grid.Col>
                ))}
              </Grid>
            </Container>
          </Box>

          {/* Steps Section */}
          <section id="how-it-works-section">
            <Box component="section" py={120}>
              <Container size="lg">
                <Stack align="center" mb={80}>
                  <Text className={classes.sectionLabel}>
                    Simple Setup Process
                  </Text>
                  <Title ta="center" className={classes.sectionTitle}>
                    From Setup to Success in{" "}
                    <Text
                      component="span"
                      className={classes.gradientText}
                      size={rem(56)}
                    >
                      Minutes
                    </Text>
                  </Title>
                </Stack>

                <Grid align="center" gutter={60}>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <motion.div
                      initial={{ opacity: 0, x: -30 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6 }}
                      viewport={{ once: true }}
                    >
                      <Box className={classes.stepsContainer}>
                        {[
                          {
                            number: "01",
                            title: "Quick Brand Setup",
                            description:
                              "Share your brand story, guidelines, and campaign goals in our intuitive interface.",
                          },
                          {
                            number: "02",
                            title: "AI Onboarding",
                            description:
                              "Your AI agent analyzes your brand and builds a custom creator matching algorithm.",
                          },
                          {
                            number: "03",
                            title: "Campaign Blueprint",
                            description:
                              "Set your budget and KPIs - the AI generates an optimized campaign strategy.",
                          },
                          {
                            number: "04",
                            title: "Launch & Scale",
                            description:
                              "Review AI-matched creators, predicted ROI, and launch your campaign with confidence.",
                          },
                        ].map((step, index) => (
                          <motion.div
                            key={step.number}
                            className={classes.step}
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: index * 0.1 }}
                            viewport={{ once: true }}
                          >
                            <div className={classes.stepNumber}>
                              {step.number}
                            </div>
                            <div className={classes.stepContent}>
                              <Title order={4} className={classes.stepTitle}>
                                {step.title}
                              </Title>
                              <Text size="sm" c="dimmed" mt="xs">
                                {step.description}
                              </Text>
                            </div>
                          </motion.div>
                        ))}
                      </Box>
                    </motion.div>
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6 }}
                      viewport={{ once: true }}
                    >
                      <div className={classes.animationContainer}>
                        <div className={classes.glowCircle} />
                        <motion.div
                          animate={{
                            y: [0, -15, 0],
                            rotate: [0, 3, 0, -3, 0],
                          }}
                          transition={{
                            duration: 6,
                            repeat: Infinity,
                            repeatType: "reverse",
                            ease: "easeInOut",
                          }}
                          className={classes.robotFigure}
                        >
                          <FaRobot
                            size={rem(140)}
                            className={classes.robotIcon}
                          />
                        </motion.div>
                        <motion.div
                          animate={{
                            scale: [1, 1.1, 1],
                            opacity: [0.5, 0.8, 0.5],
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            repeatType: "reverse",
                            ease: "easeInOut",
                          }}
                          className={classes.glowRing}
                        />
                      </div>
                    </motion.div>
                  </Grid.Col>
                </Grid>
              </Container>
            </Box>
          </section>

          {/* Sign Up Section */}
          <section id="early-access-section">
            <Box component="section" className={classes.signupSection}>
              <Container size="lg">
                <Grid gutter={{ base: 40, md: 80 }} align="center">
                  <Grid.Col
                    span={{ base: 12, md: 6 }}
                    order={{ base: 2, md: 1 }}
                  >
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                      viewport={{ once: true }}
                    >
                      <Stack gap="xl">
                        <div>
                          <Text className={classes.preTitle} mb="xs">
                            LIMITED TIME OFFER
                          </Text>
                          <Title className={classes.signupTitle}>
                            Join Our Early Access{" "}
                            <Text
                              component="span"
                              className={classes.gradientText}
                            >
                              Program
                            </Text>
                          </Title>

                          <Text
                            size="lg"
                            mt="lg"
                            className={classes.signupText}
                          >
                            Be among the first brands to leverage AI for creator
                            collaborations. We're onboarding selected brands on
                            a rolling basis.
                          </Text>
                        </div>

                        <Paper className={classes.benefitsCard} withBorder>
                          <Stack gap="md">
                            <Text fw={600} size="lg">
                              Early adopters receive:
                            </Text>

                            <div className={classes.featuresGrid}>
                              {[
                                {
                                  icon: "💎",
                                  title: "Preferred Pricing",
                                  description:
                                    "Lock in exclusive early-bird rates",
                                },
                                {
                                  icon: "🚀",
                                  title: "Priority Access",
                                  description:
                                    "First access to new AI features",
                                },
                                {
                                  icon: "👥",
                                  title: "Dedicated Support",
                                  description: "Personal account strategist",
                                },
                                {
                                  icon: "🎯",
                                  title: "Product Input",
                                  description: "Shape our product roadmap",
                                },
                              ].map((benefit, index) => (
                                <motion.div
                                  key={index}
                                  initial={{ opacity: 0, y: 10 }}
                                  whileInView={{ opacity: 1, y: 0 }}
                                  transition={{
                                    duration: 0.3,
                                    delay: index * 0.1,
                                  }}
                                  viewport={{ once: true }}
                                  className={classes.benefitItem}
                                >
                                  <Text size={rem(24)} mb={4}>
                                    {benefit.icon}
                                  </Text>
                                  <Text fw={600} size="sm">
                                    {benefit.title}
                                  </Text>
                                  <Text size="sm" c="dimmed">
                                    {benefit.description}
                                  </Text>
                                </motion.div>
                              ))}
                            </div>
                          </Stack>
                        </Paper>

                        <Group gap="xs">
                          <IconUsers size={20} style={{ opacity: 0.5 }} />
                          <Text size="sm" c="dimmed">
                            <Text span fw={600} c="white">
                              127 brands
                            </Text>{" "}
                            joined in the last 30 days
                          </Text>
                        </Group>
                      </Stack>
                    </motion.div>
                  </Grid.Col>

                  <Grid.Col
                    span={{ base: 12, md: 6 }}
                    order={{ base: 1, md: 2 }}
                  >
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      viewport={{ once: true }}
                    >
                      <Paper
                        className={classes.formWrapper}
                        radius="lg"
                        p={{ base: "md", sm: "xl" }}
                      >
                        <SignupForm userType="brand" />
                      </Paper>
                    </motion.div>
                  </Grid.Col>
                </Grid>
              </Container>
            </Box>
          </section>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default BrandLanding;
