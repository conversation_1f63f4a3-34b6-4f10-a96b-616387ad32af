"use client";

import {
  <PERSON><PERSON> as MantineButton,
  ButtonProps as MantineButtonProps,
  rem,
  useMantineTheme,
} from "@mantine/core";

interface ButtonProps extends MantineButtonProps {
  type?: any;
  onClick?: () => void;
  ref?: any;
}

const Button = (props: ButtonProps) => {
  const theme = useMantineTheme();

  return (
    <MantineButton color="#2E2E2E" radius={rem(28)} {...props}>
      {props.children}
    </MantineButton>
  );
};

export default Button;
