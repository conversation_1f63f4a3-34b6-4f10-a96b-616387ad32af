"use client";

import { Accordion, Paper, rem, Stack, Text, Title } from "@mantine/core";
import { IconPlus } from "@tabler/icons-react";
import {
  faqsDataBrand,
  faqsDataCommon,
  faqsDataCreator,
} from "../../constants/constants";
import classes from "./FaqSection.module.css";

const FaqSection = ({
  isBrand,
  isCreator,
}: {
  isBrand?: boolean;
  isCreator?: boolean;
}) => {
  const faqsData = isBrand
    ? faqsDataBrand
    : isCreator
    ? faqsDataCreator
    : faqsDataCommon;
  const items = faqsData.map((item) => (
    <Accordion.Item key={item.id} value={item.question}>
      <Accordion.Control py="md" className={classes.control} visibleFrom="lg">
        <Text size="lg" fw={400} c="#000000">
          {item.question}
        </Text>
      </Accordion.Control>
      <Accordion.Control p="xs" className={classes.control} hiddenFrom="lg">
        <Text fw={400} c="#000000">
          {item.question}
        </Text>
      </Accordion.Control>
      <Accordion.Panel w="90%">
        <Text size="lg" fw={400} c="#3C3C43D9" visibleFrom="lg">
          {item.answer}
        </Text>
        <Text
          size={rem(12)}
          fw={400}
          c="#3C3C43D9"
          hiddenFrom="lg"
          lh={rem(18)}
        >
          {item.answer}
        </Text>
      </Accordion.Panel>
    </Accordion.Item>
  ));
  return (
    <>
      <Paper
        my={rem("10%")}
        mx={rem("20%")}
        radius={rem(24)}
        p={rem(40)}
        visibleFrom="lg"
      >
        <Stack gap={rem(40)}>
          <Title
            order={1}
            size={rem(40)}
            fw={400}
            fs="italic"
            className={classes.faqSectionTitle}
          >
            Frequently Asked Questions
          </Title>
          <Accordion
            classNames={{
              chevron: classes.chevron,
              item: classes.accordionItem,
            }}
            chevron={<IconPlus className={classes.icon} />}
          >
            {items}
          </Accordion>
        </Stack>
      </Paper>

      <Paper
        my={rem("10%")}
        mx={rem(24)}
        radius={rem(16)}
        p={rem(24)}
        hiddenFrom="lg"
      >
        <Stack gap={rem(20)}>
          <Title
            order={1}
            size={rem(24)}
            fw={400}
            fs="italic"
            className={classes.faqSectionTitle}
          >
            Frequently Asked Questions
          </Title>
          <Accordion
            classNames={{
              chevron: classes.chevron,
              item: classes.accordionItem,
            }}
            chevron={<IconPlus className={classes.icon} />}
          >
            {items}
          </Accordion>
        </Stack>
      </Paper>
    </>
  );
};

export default FaqSection;
