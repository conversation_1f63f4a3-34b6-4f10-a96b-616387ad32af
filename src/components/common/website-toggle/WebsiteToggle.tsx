"use client";

import { Box, rem, SegmentedControl } from "@mantine/core";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";
import classes from "./WebsiteToggle.module.css";

const WebsiteToggle = () => {
  const router = useRouter();
  const path = usePathname();
  const isBrandPath = path === "/brands";
  const [value, setValue] = useState(isBrandPath ? "Brand" : "Creator");

  const handleSelection = (val: string) => {
    setValue(val);
    router.push(val === "Brand" ? "/brands" : "/");
  };

  return (
    <Box
      className={classes.websiteToggleContainer}
      mt={{ base: rem("12%"), md: rem("8%") }}
    >
      <SegmentedControl
        radius={rem(20)}
        data={[
          {
            value: "Brand",
            label: <span>We are a Brand</span>,
          },
          {
            value: "Creator",
            label: <span>I am a Creator</span>,
          },
        ]}
        value={value}
        onChange={handleSelection}
        color="#262626"
        classNames={{
          root: isBrandPath
            ? classes.toggleRootBrand
            : classes.toggleRootCreator,
          label: classes.toggleLabel,
          // indicator: classes.toggleIndicator,
        }}
      />
    </Box>
  );
};

export default WebsiteToggle;
