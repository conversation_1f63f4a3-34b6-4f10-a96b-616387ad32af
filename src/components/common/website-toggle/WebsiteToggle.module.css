.websiteToggleContainer {
  margin: 8% 0 36px;
}

.toggleRootCreator {
  background: #e8ecdc;
}

.toggleRootBrand {
  background: #efe7f8;
}

.toggleLabel {
  padding: 6px 18px;
  color: light-dark(#373737, #373737);
  font-size: rem(14);
  line-height: 21.87px;
  font-weight: 400;
}

.toggleLabel[data-active="true"] {
  color: #ffffff;
}

.toggleIndicator {
  animation: move-left 0.5s linear;
  animation-delay: 1s;
}

@keyframes move-left {
  0% {
    transform: translate(4px, 4px);
  }

  50% {
    transform: translate(120px, 4px);
  }

  100% {
    transform: translate(4px, 4px);
  }
}
