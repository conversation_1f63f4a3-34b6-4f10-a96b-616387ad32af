import {
  Center,
  Container,
  Flex,
  rem,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import Link from "next/link";
import Button from "../../button/Button";
import {
  COLECTA_APP_REQUEST_DEMO_URL,
  COLECTA_APP_SIGNUP_URL,
} from "../../constants/urls";
import classes from "./AboutHero.module.css";
import AboutHeroChips from "./AboutHeroChips";

const HeroSectionMobile = () => {
  return (
    <Stack hiddenFrom="lg">
      <Container className={classes.heroSectionTitleContainer}>
        <Stack align="center">
          <Text size={rem(16)} fw={300}>
            About Us
          </Text>
          <Stack align="center" ta="center">
            <Title order={1} size={rem(26)} fw={400} w="85%">
              Building The Future Of Brand-Creator Partnerships
            </Title>
            <Center>
              <Text fw={300} size={rem(14)} lh={rem(20)} ta="center" maw="85%">
                At Colecta.ai, we believe the best collaborations happen when
                brands and creators truly understand and complement each other.
              </Text>
            </Center>
          </Stack>
        </Stack>
        <Stack align="center" gap="sm" my="xl">
          <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
            <Button size="md" fw={400} bg="#E7C1FE" c="#000">
              Brand - Book A Demo
            </Button>
          </Link>
          <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
            <Button size="md" color="#C4EE34" c="#000" fw={400}>
              Creator - Early Access
            </Button>
          </Link>
        </Stack>
      </Container>
      <AboutHeroChips isMobile />
    </Stack>
  );
};

const HeroSectionDesktop = () => {
  return (
    <Stack align="center" visibleFrom="lg">
      <Text size={rem(20)} fw={300}>
        About Us
      </Text>
      <Title
        mt={rem(24)}
        order={1}
        size={rem(75)}
        fw={400}
        lh={rem(50)}
        className={classes.heroSectionTitle}
      >
        Building The Future Of
      </Title>
      <Title
        order={1}
        size={rem(84)}
        fw={400}
        lh={rem(100)}
        fs="italic"
        className={classes.heroSectionTitle2}
      >
        Brand-Creator Partnerships
      </Title>
      <Center mt={rem(20)}>
        <Text fw={300} size={rem(28)} lh={rem(40)} ta="center" maw="60%">
          At Colecta.ai, we believe the best collaborations happen when brands
          and creators truly understand and complement each other.
        </Text>
      </Center>
      <Flex gap={rem(18)} mt="lg">
        <Link
          style={{ width: "100%" }}
          href={COLECTA_APP_REQUEST_DEMO_URL}
          target="_blank"
        >
          <Button w="100%" size="md" fw={400} bg="#E7C1FE" c="#000">
            Brand - Book A Demo
          </Button>
        </Link>
        <Link
          href={COLECTA_APP_SIGNUP_URL}
          style={{ width: "100%" }}
          target="_blank"
        >
          <Button size="md" w="100%" color="#C4EE34" c="#000" fw={400}>
            Creator - Early Access
          </Button>
        </Link>
      </Flex>
      <AboutHeroChips />
    </Stack>
  );
};

const AboutHero = () => {
  return (
    <Stack align="center" mt={{ base: "25%", md: "8%" }}>
      <HeroSectionDesktop />
      <HeroSectionMobile />
    </Stack>
  );
};

export default AboutHero;
