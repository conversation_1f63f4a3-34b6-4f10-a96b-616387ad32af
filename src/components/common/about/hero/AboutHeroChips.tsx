import { Badge } from "@mantine/core";
import {
  MouseParallaxChild,
  MouseParallaxContainer,
} from "react-parallax-mouse";
import classes from "./AboutHero.module.css";

const AboutHeroChips = ({ isMobile }: { isMobile?: boolean }) => {
  return (
    <MouseParallaxContainer
      globalFactorX={0.1}
      globalFactorY={0.1}
      className={
        isMobile
          ? classes.heroSectionContainerMobile
          : classes.heroSectionContainer
      }
      resetOnLeave
    >
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "1%",
          left: "5%",
        }}
      >
        <Badge color="#E1BFF6" c="#000" size="sm" tt="capitalize">
          #collaboration
        </Badge>
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "75%",
          left: "8%",
        }}
      >
        {!isMobile ? (
          <Badge color="#E1BFF6" c="#000" size="sm" tt="capitalize">
            #AiPowered
          </Badge>
        ) : (
          <></>
        )}
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "35%",
          left: "38%",
        }}
      >
        {isMobile ? (
          <Badge color="#E1BFF6" c="#000" size="sm" tt="capitalize">
            #AiPowered
          </Badge>
        ) : (
          <></>
        )}
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "35%",
          left: "18%",
        }}
      >
        {!isMobile ? (
          <Badge color="#C4EE34" c="#000" size="sm" tt="capitalize">
            #creators
          </Badge>
        ) : (
          <></>
        )}
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "15%",
          left: "30%",
        }}
      >
        {isMobile ? (
          <Badge color="#C4EE34" c="#000" size="sm" tt="capitalize">
            #creators
          </Badge>
        ) : (
          <></>
        )}
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "20%",
          left: "90%",
        }}
      >
        {!isMobile ? (
          <Badge color="#C4EE34" c="#000" size="sm" tt="capitalize">
            #campaigns
          </Badge>
        ) : (
          <></>
        )}
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "60%",
          left: "10%",
        }}
      >
        {isMobile ? (
          <Badge color="#C4EE34" c="#000" size="sm" tt="capitalize">
            #campaigns
          </Badge>
        ) : (
          <></>
        )}
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "50%",
          left: "80%",
        }}
      >
        <Badge color="#E1BFF6" c="#000" size="sm" tt="capitalize">
          #insights
        </Badge>
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "70%",
          left: "88%",
        }}
      >
        {!isMobile ? (
          <Badge color="#C4EE34" c="#000" size="sm" tt="capitalize">
            #growth
          </Badge>
        ) : (
          <></>
        )}
      </MouseParallaxChild>
      <MouseParallaxChild
        factorX={0.2}
        factorY={0.7}
        className={classes.tags}
        style={{
          top: "3%",
          left: "78%",
        }}
      >
        {isMobile ? (
          <Badge color="#C4EE34" c="#000" size="sm" tt="capitalize">
            #growth
          </Badge>
        ) : (
          <></>
        )}
      </MouseParallaxChild>
    </MouseParallaxContainer>
  );
};

export default AboutHeroChips;
