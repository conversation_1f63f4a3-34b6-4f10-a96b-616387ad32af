import { Flex, Image, Paper, rem, Stack, Text, Title } from "@mantine/core";
import React from "react";
import classes from "./WhyUsSection.module.css";

const WhyUsSectionWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <Paper
        px={rem(120)}
        py={rem(96)}
        radius={rem(72)}
        mx="5%"
        mt="10%"
        mb="7%"
        visibleFrom="lg"
      >
        <Stack gap={rem(120)}>{children}</Stack>
      </Paper>
      <Paper
        px={rem(40)}
        py={rem(24)}
        radius={rem(24)}
        mx="5%"
        mt="10%"
        mb="7%"
        hiddenFrom="lg"
      >
        <Stack gap={rem(64)}>{children}</Stack>
      </Paper>
    </>
  );
};

const WhyUsSectionPointsWrapper = ({
  children,
  title,
}: {
  children: React.ReactNode;
  title: string;
}) => {
  return (
    <Stack gap={rem(48)}>
      <Title
        c="#231C19"
        fw={400}
        order={2}
        size={rem(32)}
        fs="italic"
        className={classes.titleText}
      >
        {title}
      </Title>
      <Flex gap={rem(48)} align="center" visibleFrom="lg">
        {children}
      </Flex>
      <Stack gap={rem(48)} align="center" hiddenFrom="lg">
        {children}
      </Stack>
    </Stack>
  );
};

const WhyUsSection = () => {
  return (
    <WhyUsSectionWrapper>
      <WhyUsSectionPointsWrapper title="For Brands">
        <Stack w={{ base: "100%", md: "33%" }}>
          <Image src="/about-brand-1.png" w={rem(36)} />
          <Text fw={400} size={rem(20)} c="#373737">
            Find Creators Who Deliver Results
          </Text>
          <Text c="#37373780" fw={400}>
            Colecta. ai helps brands find creators who understand your vision,
            engage with your target audience effectively, and contribute to
            building a lasting impact.
          </Text>
        </Stack>
        <Stack w={{ base: "100%", md: "33%" }}>
          <Image src="/about-brand-2.png" w={rem(36)} />
          <Text fw={400} size={rem(20)} c="#373737">
            Effortless Matchmaking
          </Text>
          <Text c="#37373780" fw={400}>
            Colecta. Ai uses advanced intelligent technology to understand your
            needs and discover a match according to your metrics. Leave the task
            of finding your perfect match to us.
          </Text>
        </Stack>
        <Stack w={{ base: "100%", md: "33%" }}>
          <Image src="/about-brand-3.png" w={rem(36)} />
          <Text fw={400} size={rem(20)} c="#373737">
            Data-Driven Decision Making
          </Text>
          <Text c="#37373780" fw={400}>
            Insights derived from thorough data analysis are rooted in facts,
            helping brands evaluate creators' portfolios and make informed
            decisions.
          </Text>
        </Stack>
      </WhyUsSectionPointsWrapper>
      <WhyUsSectionPointsWrapper title="For Creators">
        <Stack w={{ base: "100%", md: "33%" }}>
          <Image src="/about-creator-1.png" w={rem(36)} />
          <Text fw={400} size={rem(20)} c="#373737">
            Discover Your Perfect Match
          </Text>
          <Text c="#37373780" fw={400}>
            Colecta.ai connects you with collaborations tailored to your niche,
            audience, and style, ensuring you partner with brands that truly
            align with your values and goals.
          </Text>
        </Stack>
        <Stack w={{ base: "100%", md: "33%" }}>
          <Image src="/about-creator-2.png" w={rem(36)} />
          <Text fw={400} size={rem(20)} c="#373737">
            Save Time with Smart Matchmaking
          </Text>
          <Text c="#37373780" fw={400}>
            Let AI do the heavy lifting! Colecta.ai recommends the best
            collaborations for you, so you can focus on creating amazing content
            instead of endlessly searching for opportunities.
          </Text>
        </Stack>
        <Stack w={{ base: "100%", md: "33%" }}>
          <Image src="/about-creator-3.png" w={rem(36)} />
          <Text fw={400} size={rem(20)} c="#373737">
            Get the Insights You Deserve
          </Text>
          <Text c="#37373780" fw={400}>
            Track your performance with detailed analytics, understand your
            audience better, and use data-driven recommendations to set fair
            pricing and grow your influence.
          </Text>
        </Stack>
      </WhyUsSectionPointsWrapper>
    </WhyUsSectionWrapper>
  );
};

export default WhyUsSection;
