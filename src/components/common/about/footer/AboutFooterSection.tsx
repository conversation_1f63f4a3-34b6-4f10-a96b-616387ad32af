import { Box, Flex, rem, Stack, Text } from "@mantine/core";
import { IconArrowRight } from "@tabler/icons-react";
import Link from "next/link";
import Button from "../../button/Button";
import {
  COLECTA_APP_REQUEST_DEMO_URL,
  COLECTA_APP_SIGNUP_URL,
  COLECTA_MAIL_TO,
} from "../../constants/urls";
import classes from "./AboutFooterSection.module.css";

const AboutFooterMobile = () => {
  return (
    <Stack hiddenFrom="lg">
      <Box
        className={classes.topContainerMobile}
        pr="xl"
        py={rem(52)}
        h={rem(530)}
        w="100%"
      >
        <Stack gap={rem(20)} align="flex-end" pl={rem(72)}>
          <Text
            size={rem(28)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Join Us on This Journey
          </Text>
          <Text
            size={rem(16)}
            fw={300}
            w="100%"
            lh={rem(28)}
            c="#231C19"
            ta="right"
          >
            At Colecta.ai, we’re not just building a platform—we’re creating a
            movement.
          </Text>
        </Stack>
        <Stack w="100%" mt={rem(120)} gap={rem(16)} px="xl">
          <Link
            style={{ width: "100%" }}
            href={COLECTA_APP_REQUEST_DEMO_URL}
            target="_blank"
          >
            <Button w="100%" size="xl" fw={400} bg="#E7C1FE" c="#000">
              Book A Demo
            </Button>
          </Link>
          <Link
            href={COLECTA_APP_SIGNUP_URL}
            style={{ width: "100%" }}
            target="_blank"
          >
            <Button size="xl" w="100%" color="#C4EE34" c="#000" fw={400}>
              Sign up As A Creator
            </Button>
          </Link>
        </Stack>
      </Box>
      <Stack
        pb={rem(32)}
        pt={rem(120)}
        px={rem(24)}
        gap={rem(32)}
        h={rem(340)}
        className={classes.bottomContainerMobile}
        justify="space-between"
      >
        <Stack>
          <Text
            size={rem(24)}
            fw={400}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Are you agencies leveling up?
          </Text>
          <Text size={rem(16)} fw={300} lh={rem(22)}>
            Empowering agencies to excel. Let’s elevate your game together.
          </Text>
        </Stack>
        <Link href={COLECTA_MAIL_TO} target="_blank">
          <Button
            fs="italic"
            size="lg"
            color="#373737"
            fw={500}
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
              />
            }
          >
            Agencies? Let’s Chat
          </Button>
        </Link>
      </Stack>
    </Stack>
  );
};

const AboutFooterDesktop = () => {
  return (
    <Stack my="5%" gap="0" mx="7%" visibleFrom="lg">
      <Box
        className={classes.topContainer}
        px={rem(72)}
        py={rem(52)}
        h={rem(530)}
        w="100%"
      >
        <Stack gap={rem(20)}>
          <Text
            size={rem(40)}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Join Us on This Journey
          </Text>
          <Text size={rem(18)} fw={300} w="40%" lh={rem(28)} c="#231C19">
            At Colecta.ai, we’re not just building a platform—we’re creating a
            movement. Whether you’re looking to invest in the future of
            brand-creator partnerships or join a team that’s reshaping an
            industry, we’d love to have you with us.
          </Text>
        </Stack>
        <Flex w="100%" mt={rem(120)} pl={rem(300)} gap={rem(18)}>
          <Link
            style={{ width: "100%" }}
            href={COLECTA_APP_REQUEST_DEMO_URL}
            target="_blank"
          >
            <Button mt="xl" w="100%" size="xl" fw={400} bg="#E7C1FE" c="#000">
              Book A Demo
            </Button>
          </Link>
          <Link
            href={COLECTA_APP_SIGNUP_URL}
            style={{ width: "100%" }}
            target="_blank"
          >
            <Button
              mt="xl"
              size="xl"
              w="100%"
              color="#C4EE34"
              c="#000"
              fw={400}
            >
              Sign up As A Creator
            </Button>
          </Link>
        </Flex>
      </Box>
      <Flex
        py={rem(56)}
        px={rem(60)}
        className={classes.bottomContainer}
        h={rem(494)}
        align="flex-end"
        justify="space-between"
      >
        <Stack>
          <Text
            size={rem(28)}
            fw={400}
            c="#444443"
            className={classes.headingText}
            fs="italic"
          >
            Are you agencies leveling up?
          </Text>
          <Text size={rem(18)} fw={300}>
            Empowering agencies to excel. Let’s elevate your game together.
          </Text>
        </Stack>
        <Link href={COLECTA_MAIL_TO} target="_blank">
          <Button
            fs="italic"
            size="md"
            color="#373737"
            fw={500}
            rightSection={
              <IconArrowRight
                style={{ width: rem(16), height: rem(16), marginLeft: rem(36) }}
              />
            }
          >
            Agencies? Let’s Chat
          </Button>
        </Link>
      </Flex>
    </Stack>
  );
};

const AboutFooterSection = () => {
  return (
    <>
      <AboutFooterDesktop />
      <AboutFooterMobile />
    </>
  );
};

export default AboutFooterSection;
