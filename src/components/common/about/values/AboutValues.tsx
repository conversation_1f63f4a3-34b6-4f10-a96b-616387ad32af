import { Flex, Image, Paper, rem, Stack, Text, Title } from "@mantine/core";

const ValueCard = ({
  title,
  desc,
  img,
}: {
  title: string;
  desc: string;
  img: string;
}) => {
  return (
    <>
      <Paper
        radius={rem(18)}
        withBorder
        py={rem(24)}
        px={rem(32)}
        h={rem(280)}
        w="33%"
        visibleFrom="lg"
      >
        <Stack h="100%" gap={rem(30)} justify="space-between">
          <Image src={img} w="fit-content" h={rem(48)} />
          <Stack gap={rem(6)}>
            <Text size={rem(20)} fw={400} c="#000000CC">
              {title}
            </Text>
            <Text c="#00000080" fw={400}>
              {desc}
            </Text>
          </Stack>
        </Stack>
      </Paper>

      <Paper radius={rem(18)} withBorder p={rem(24)} hiddenFrom="lg">
        <Stack h="100%" gap={rem(32)} justify="space-between">
          <Image src={img} w="fit-content" maw={rem(60)} h={rem(40)} />
          <Stack gap={rem(6)}>
            <Text size={rem(16)} fw={400} c="#000000CC">
              {title}
            </Text>
            <Text c="#00000080" size="sm" fw={400}>
              {desc}
            </Text>
          </Stack>
        </Stack>
      </Paper>
    </>
  );
};

const AboutValuesTitleMobile = () => {
  return (
    <Flex justify="center" hiddenFrom="lg">
      <Stack align="center" gap={rem(8)}>
        <Text size="xs" tt="uppercase" lh={rem(12)} c="#00000099">
          Our Foundational Beliefs
        </Text>
        <Title order={1} size={rem(28)} fw={400}>
          What Drives Us Forward
        </Title>
        <Text fw={400} lh={rem(25)} size={rem(14)} c="#000000CC" ta="center">
          At Colecta. ai, we find our roots imbibed into the principles of
          Empathy, Transparency and Innovation. These values shape who we are
          and guide everything we do to deliver impactful, authentic
          partnerships. For us, these are not just words, but our very
          foundation!
        </Text>
      </Stack>
    </Flex>
  );
};
const AboutValuesTitleDesktop = () => {
  return (
    <Flex justify="center" visibleFrom="lg">
      <Stack align="center" gap={rem(8)}>
        <Text size="sm" tt="uppercase" lh={rem(12)} c="#00000099">
          Our Foundational Beliefs
        </Text>
        <Title order={1} size={rem(36)} fw={400}>
          What Drives Us Forward
        </Title>
        <Text
          fw={400}
          lh={rem(25)}
          size={rem(16)}
          c="#000000CC"
          ta="center"
          w="50%"
        >
          At Colecta. ai, we find our roots imbibed into the principles of
          Empathy, Transparency and Innovation. These values shape who we are
          and guide everything we do to deliver impactful, authentic
          partnerships. For us, these are not just words, but our very
          foundation!
        </Text>
      </Stack>
    </Flex>
  );
};

const ValuesCardWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <Flex justify="center" gap={rem(42)} mx="7%" mt="5%" visibleFrom="lg">
        {children}
      </Flex>
      <Stack align="center" gap={rem(24)} mt="5%" hiddenFrom="lg">
        {children}
      </Stack>
    </>
  );
};

const AboutValues = () => {
  return (
    <Stack mb="7%" mx={rem(40)}>
      <AboutValuesTitleDesktop />
      <AboutValuesTitleMobile />
      <ValuesCardWrapper>
        <ValueCard
          title="Empathy"
          desc=" We are aware of the hurdles that the brands and creators face
                when it comes to building genuine, impactful partnerships. At
                Colecta. ai, we're here to make it easier, helping you turn
                challenges into opportunities for success."
          img="/about-values1.png"
        />
        <ValueCard
          title="Transparency"
          desc="At Colecta. ai, transparency isn’t just a value- it is how we
                build trust. With clear processes right from early communication
                to the final step of payment, we ensure brands and creators
                foster partnerships rooted in clarity and confidence."
          img="/about-values2.png"
        />
        <ValueCard
          title="Innovation"
          desc=" Innovation fuels everything we do here at Colecta. ai. We aim to
                stay ahead of time and trends by being dynamic and contributing
                to make every brand-creator partnership smarter and more
                effective. We don’t follow the trends, we set them!"
          img="/about-values3.png"
        />
      </ValuesCardWrapper>
    </Stack>
  );
};

export default AboutValues;
