"use client";

import { ActionIcon, Box, Flex, Image, rem, Stack, Text } from "@mantine/core";
import {
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandTwitter,
} from "@tabler/icons-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { forwardRef } from "react";
import {
  COLECTA_INSTAGRAM,
  COLECTA_LINKEDIN,
  COLECTA_TWITTER,
} from "../common/constants/urls";
import classes from "./Footer.module.css";

const FooterMobileContent = forwardRef<HTMLDivElement, any>(
  ({ styles, navigateToHome }, ref) => {
    return (
      <Box w="100%" ml="0" ref={ref} style={styles} hiddenFrom="lg">
        <Stack p={rem(24)}>
          <Flex justify="space-between">
            <Image
              src="./colecta-logo.svg"
              w="100%"
              h={rem(32)}
              onClick={navigateToHome}
              style={{ cursor: "pointer" }}
              mr="lg"
            />
            <Flex align="center" gap={rem(16)}>
              <Link href={COLECTA_LINKEDIN} target="_blank">
                <ActionIcon variant="transparent">
                  <IconBrandLinkedin
                    style={{
                      width: rem(20),
                      height: rem(20),
                      color: "#000000",
                    }}
                  />
                </ActionIcon>
              </Link>
              <Link href={COLECTA_INSTAGRAM} target="_blank">
                <ActionIcon variant="transparent">
                  <IconBrandInstagram
                    style={{
                      width: rem(20),
                      height: rem(20),
                      color: "#000000",
                    }}
                  />
                </ActionIcon>
              </Link>
              <Link href={COLECTA_TWITTER} target="_blank">
                <ActionIcon variant="transparent">
                  <IconBrandTwitter
                    style={{
                      width: rem(20),
                      height: rem(20),
                      color: "#000000",
                    }}
                  />
                </ActionIcon>
              </Link>
            </Flex>
          </Flex>
          <Flex justify="space-between">
            <Text size="sm" c="#000000CC">
              Terms & Conditions
            </Text>
            <Text size="sm" c="#000000CC">
              Privacy Policy
            </Text>
            <Text size="sm" c="#000000CC">
              2024 Colecta, Inc ©
            </Text>
          </Flex>
        </Stack>
      </Box>
    );
  }
);

const FooterDesktopContent = forwardRef<HTMLDivElement, any>(
  ({ styles, navigateToHome }, ref) => {
    return (
      <Box
        className={classes.footerContainer}
        w={{ base: "100%", md: rem("80%") }}
        ml={{ base: 0, md: rem("10%") }}
        ref={ref}
        style={styles}
        visibleFrom="lg"
      >
        <Flex justify="space-between" px={rem(70)} pb={rem(20)} pt={rem(40)}>
          <Flex align="center" gap={rem(24)}>
            <Image
              src="./colecta-logo.svg"
              w="100%"
              h={rem(36)}
              onClick={navigateToHome}
              style={{ cursor: "pointer" }}
              mr="lg"
            />
            <Text mt={rem(2)} c="#000000CC">
              Terms & Conditions
            </Text>
            <Text c="#000000CC" mt={rem(2)}>
              Privacy Policy
            </Text>
          </Flex>
          <Flex align="center" gap={rem(16)}>
            <Link href={COLECTA_LINKEDIN} target="_blank">
              <ActionIcon variant="transparent">
                <IconBrandLinkedin stroke={1.5} />
              </ActionIcon>
            </Link>
            <Link href={COLECTA_INSTAGRAM} target="_blank">
              <ActionIcon variant="transparent">
                <IconBrandInstagram stroke={1.5} />
              </ActionIcon>
            </Link>
            <Link href={COLECTA_TWITTER} target="_blank">
              <ActionIcon variant="transparent">
                <IconBrandTwitter stroke={1.5} />
              </ActionIcon>
            </Link>
            <Text mt={rem(2)} c="#000000CC">
              2024 Colecta, Inc ©
            </Text>
          </Flex>
        </Flex>
      </Box>
    );
  }
);

const Footer = forwardRef<HTMLDivElement, any>(({ styles }, ref) => {
  const router = useRouter();

  const navigateToAbout = () => {
    router.push("/about");
  };

  const navigateToHome = () => {
    router.push("/");
  };

  const navigateToEarlyAccess = () => {
    router.push("/about");
  };

  return (
    <>
      <FooterDesktopContent
        styles={styles}
        navigateToHome={navigateToHome}
        ref={ref}
      />
      <FooterMobileContent
        styles={styles}
        navigateToHome={navigateToHome}
        ref={ref}
      />
    </>
  );
});

export default Footer;
