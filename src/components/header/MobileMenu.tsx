import { ActionIcon, Drawer, rem, Stack } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { IconArrowRight, IconMenu2, IconX } from "@tabler/icons-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import Button from "../common/button/Button";
import {
  COLECTA_APP_REQUEST_DEMO_URL,
  COLECTA_APP_SIGNUP_URL,
} from "../common/constants/urls";
import classes from "./Header.module.css";

const MobileMenu = ({
  isCreator,
  isBrand,
}: {
  isCreator?: boolean;
  isBrand?: boolean;
}) => {
  const router = useRouter();
  const [showMenu, { toggle, close }] = useDisclosure(false);

  const navigateToAbout = () => {
    router.push("/about");
  };

  const navigateToHome = () => {
    router.push("/");
  };

  const navigateToBrands = () => {
    router.push("/brands");
  };

  return (
    <>
      <ActionIcon variant="transparent" hiddenFrom="lg" onClick={toggle}>
        {showMenu ? <IconX color="#212121" /> : <IconMenu2 color="#212121" />}
      </ActionIcon>
      <Drawer
        opened={showMenu}
        onClose={close}
        position="right"
        hiddenFrom="lg"
        size={rem("100vw")}
        withCloseButton={false}
        transitionProps={{
          transition: "fade",
          duration: 200,
          timingFunction: "ease",
        }}
        classNames={{
          content: isCreator
            ? classes.creatorMenu
            : isBrand
            ? classes.brandMenu
            : classes.commonMenu,
        }}
      >
        <Stack mt={rem(80)}>
          {!isCreator && !isBrand && (
            <>
              <Button
                size="xl"
                color="#F1EDEB"
                c="#000"
                fw={500}
                w="100%"
                justify="flex-start"
                onClick={navigateToBrands}
              >
                Brands
              </Button>
              <Button
                size="xl"
                color="#F1EDEB"
                c="#000"
                fw={500}
                w="100%"
                justify="flex-start"
                onClick={navigateToHome}
              >
                Creators
              </Button>
            </>
          )}
          {(isCreator || isBrand) && (
            <Button
              size="xl"
              color={isCreator ? "#E8ECDC" : "#EFE7F8"}
              c="#000"
              fw={500}
              w="100%"
              justify="flex-start"
              onClick={navigateToAbout}
            >
              About
            </Button>
          )}
          {!isCreator && (
            <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
              <Button
                fs="italic"
                size="xl"
                color="#E7C1FE"
                c="#000"
                fw={500}
                w="100%"
                rightSection={
                  <IconArrowRight
                    style={{
                      width: rem(20),
                      height: rem(20),
                      fontWeight: 500,
                    }}
                  />
                }
                justify="space-between"
              >
                Book A Demo
              </Button>
            </Link>
          )}
          {!isBrand && (
            <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
              <Button
                fs="italic"
                size="xl"
                color="#C4EE34"
                fw={500}
                c="#000"
                w="100%"
                rightSection={
                  <IconArrowRight
                    style={{
                      width: rem(20),
                      height: rem(20),
                      fontWeight: 500,
                    }}
                  />
                }
                justify="space-between"
              >
                Creator Sign Up
              </Button>
            </Link>
          )}
        </Stack>
      </Drawer>
    </>
  );
};

export default MobileMenu;
