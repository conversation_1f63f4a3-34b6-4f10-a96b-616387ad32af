"use client";

import { Box, Flex, Image, rem, Text } from "@mantine/core";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import Button from "../common/button/Button";
import {
  COLECTA_APP_REQUEST_DEMO_URL,
  COLECTA_APP_SIGNUP_URL,
} from "../common/constants/urls";
import classes from "./Header.module.css";
import MobileMenu from "./MobileMenu";

const Header = ({
  styles,
  isCreator,
  isBrand,
}: {
  styles: any;
  isCreator?: boolean;
  isBrand?: boolean;
}) => {
  const router = useRouter();
  const pathname = usePathname();

  const isPathAbout = pathname.startsWith("/about");
  const isPathBrand = pathname.startsWith("/brands");

  const navigateToAbout = () => {
    router.push("/about");
  };

  const navigateToBrands = () => {
    router.push("/brands");
  };

  const navigateToHome = () => {
    router.push("/");
  };

  return (
    <Box
      className={classes.headerContainer}
      w={{ base: rem("90%"), md: rem("60%") }}
      left={{ base: rem("5%"), md: rem("20%") }}
      style={styles}
    >
      <Flex justify="space-between" px={rem(24)} py={rem(16)} align="center">
        <Flex gap={rem(24)}>
          <Image
            src="./colecta-logo.svg"
            w="100%"
            h={{ base: rem(32), md: rem(36) }}
            onClick={navigateToHome}
            style={{ cursor: "pointer" }}
          />
          <Flex visibleFrom="lg">
            <Button variant="transparent" onClick={navigateToHome}>
              <Text c="#373737">For Creators</Text>
            </Button>
            <Button variant="transparent" onClick={navigateToBrands}>
              <Text c="#373737">For Brands</Text>
            </Button>
          </Flex>
        </Flex>
        <Flex visibleFrom="lg">
          {!isPathAbout && (
            <Button variant="transparent" onClick={navigateToAbout}>
              <Text c="#373737">About us</Text>
            </Button>
          )}
          {!isPathBrand && (
            <Link href={COLECTA_APP_SIGNUP_URL} target="_blank">
              <Button fs="italic" ml="sm">
                Early Access
              </Button>
            </Link>
          )}
          {isPathBrand && (
            <Link href={COLECTA_APP_REQUEST_DEMO_URL} target="_blank">
              <Button fs="italic" ml="sm">
                Book a Demo
              </Button>
            </Link>
          )}
        </Flex>
        <MobileMenu isCreator={isCreator} isBrand={isBrand} />
      </Flex>
    </Box>
  );
};

export default Header;
