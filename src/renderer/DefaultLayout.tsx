"use client";

import WebsiteToggle from "@/components/common/website-toggle/WebsiteToggle";
import Footer from "@/components/footer/Footer";
import Header from "@/components/header/Header";
import classes from "@/renderer/base.module.css";
import Hotjar from "@hotjar/browser";
import { Center, Transition } from "@mantine/core";
import { useInViewport } from "@mantine/hooks";
import { useEffect } from "react";

const siteId = 5223955;
const hotjarVersion = 6;
interface DefaultLayoutProps {
  children: React.ReactNode;
  showToggle?: boolean;
  isCreator?: boolean;
  isBrand?: boolean;
}

const DefaultLayout = (props: DefaultLayoutProps) => {
  const {
    children,
    showToggle = true,
    isCreator = false,
    isBrand = false,
  } = props;
  const { ref, inViewport } = useInViewport();

  useEffect(() => {
    Hotjar.init(siteId, hotjarVersion);
  }, []);

  return (
    <div
      className={
        isCreator
          ? classes.bodyContainerCreator
          : isBrand
          ? classes.bodyContainerBrand
          : classes.bodyContainerCommon
      }
    >
      <Transition
        mounted={!inViewport}
        transition="slide-down"
        duration={400}
        timingFunction="ease"
      >
        {(styles) => (
          <Header
            isCreator={isCreator}
            isBrand={isBrand}
            styles={{
              ...styles,
              backgroundColor: isCreator
                ? "#F8FBEC"
                : isBrand
                ? "#FAF3FF"
                : "#F4ECE8",
              border: "2px solid",
              borderTop: "none",
              borderLeft: "none",
              borderRight: "none",
              borderColor: isCreator
                ? "#DFEEAC"
                : isBrand
                ? "#E7C1FE"
                : "#EAD8CF",
            }}
          />
        )}
      </Transition>

      {showToggle && (
        <Center>
          <WebsiteToggle />
        </Center>
      )}
      {children}

      <Footer
        ref={ref}
        styles={{
          backgroundColor: isCreator
            ? "#F8FBEC"
            : isBrand
            ? "#FAF3FF"
            : "#F4ECE8",
        }}
      />
    </div>
  );
};

export default DefaultLayout;
