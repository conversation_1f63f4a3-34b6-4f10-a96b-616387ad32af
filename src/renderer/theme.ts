// src/styles/theme.ts
import { createTheme, MantineColorsTuple } from '@mantine/core';

// Pastel vibrant color palette
export const themeColors = {
    primary: '#8A6FDF', // Vibrant purple
    secondary: '#64D9D0', // Teal
    accent1: '#FF9B82', // Coral
    accent2: '#FFD98F', // Amber
    accent3: '#A1C6FF', // Sky blue
    dark: '#121212', // Dark background
    darkSecondary: '#1e1e1e', // Slightly lighter dark
    darkTertiary: '#282828', // For cards and elements
    light: '#FAFAFA',
    text: '#E0E0E0', // Light text for dark theme
    textSecondary: '#ABABAB', // Secondary text
};

// Gradients
export const gradients = {
    primaryGradient: 'linear-gradient(135deg, #8A6FDF 0%, #B469FF 100%)',
    secondaryGradient: 'linear-gradient(135deg, #64D9D0 0%, #4FFBEB 100%)',
    accentGradient: 'linear-gradient(135deg, #FF9B82 0%, #FFD98F 100%)',
};

// Define color palettes
const purplePalette: MantineColorsTuple = [
    '#F0E9FF',
    '#D9CBFF',
    '#C3ADFF',
    '#AD8FFF',
    '#9775FF',
    '#8A6FDF', // Primary
    '#7B60D6',
    '#6B51CC',
    '#5C42C0',
    '#4D33B4',
];

const tealPalette: MantineColorsTuple = [
    '#E1FFFD',
    '#BFFFFC',
    '#9DFFFB',
    '#7AFFF9',
    '#57FFF7',
    '#64D9D0', // Secondary
    '#55C6BE',
    '#46B3AB',
    '#379F98',
    '#288B84',
];

export const colectaTheme = createTheme({
    fontFamily: 'Inter, sans-serif',
    headings: {
        fontFamily: 'Inter, sans-serif',
        fontWeight: '700',
    },
    colors: {
        purple: purplePalette,
        teal: tealPalette,
        dark: [
            themeColors.light, // text
            themeColors.textSecondary, // dimmed text
            '#909296', // muted text
            '#5C5F66', // subtle text
            '#373A40', // borders
            themeColors.darkTertiary, // backgrounds
            themeColors.darkSecondary, // components background
            themeColors.dark, // page background
            '#141517', // darker backgrounds
            '#101113', // darkest backgrounds
        ],
    },
    primaryColor: 'purple',
    primaryShade: 5,
    defaultRadius: 'md',
    defaultGradient: {
        from: 'purple.5',
        to: 'purple.8',
        deg: 45,
    },

    components: {
        Button: {
            defaultProps: {
                radius: 'md',
            },
            styles: {
                root: {
                    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 5px 15px rgba(0, 0, 0, 0.15)',
                    },
                },
            },
        },
        Card: {
            defaultProps: {
                radius: 'lg',
                p: 'xl',
            },
            styles: {
                root: {
                    backgroundColor: themeColors.darkTertiary,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: '0 15px 30px rgba(0, 0, 0, 0.15)',
                    },
                },
            },
        },
        Input: {
            defaultProps: {
                radius: 'md',
            },
            styles: {
                input: {
                    backgroundColor: themeColors.darkSecondary,
                    borderColor: themeColors.darkTertiary,
                    color: themeColors.text,
                    '&:focus': {
                        borderColor: themeColors.primary,
                    },
                },
            },
        },
    },
    other: {
        gradients,
    },
});